import Vue from "vue";
import VueRouter from "vue-router";
import Index from "../views/Home/Index";
import question from "../views/Home/question";
import jkglUrl from "../views/Home/jkglUrl";
import notice from "../views/Home/notice";
import test from "../views/Home/test";
import PersonIndex from "../views/Persons/PersonIndex";
import PersonalAppointment from "../views/Persons/PersonalAppointment";
import VehicleIndex from "../views/Persons/VehicleIndex";
import VehicleIndexs from "../views/Persons/VehicleIndexs";
import StaffIndex from "../views/Persons/StaffIndex";
import PersonDetails from "../views/Persons/PersonDetails";
import PersonBookSum from "../views/Persons/PersonBookSum";
import PersonOrders from "../views/Persons/PersonOrders";
import PersonPayment from "../views/Persons/PersonPayment";
import TeamLogin from "../views/Team/TeamLogin";
import TeamBookSum from "../views/Team/TeamBookSum";
import TeamDetails from "../views/Team/TeamDetails";
import TeamOrders from "../views/Team/TeamOrders";
import TeamAddClusItem from "../views/Team/TeamAddClusItem.vue";
import MyOrderList from "../views/Orders/MyOrderList";
import OrderDetails from "../views/Orders/OrderDetails";
import PayRecord from "../views/Orders/PayRecord";
import ReportLogin from "../views/Report/ReportLogin";
// import ReportDataList from '../views/Report/ReportDataList'
// import ReportDetails from '../views/Report/ReportDetails'
import oauth from "../views/oauth";
import login from "../views/login";
import ReportList from "../views/Report/NewReport/ReportList";
import ReportMian from "../views/Report/NewReport/ReportMian";
import ReportFinal from "../views/Report/NewReport/ReportFinal";
import ReportGuide from "../views/Report/NewReport/ReportGuide";
import ReportSuggest from "../views/Report/NewReport/ReportSuggest";
import ReportDetails from "../views/Report/NewReport/ReportDetails";
import addClusItem from "../views/addClusItem";
import SweepCodePay from "../views/SweepCodePay/SweepCodePay";
import SweepOtherPay from "../views/SweepCodePay/SweepOtherPay";
import { storage } from "../common/index";
import TeamChooseClus from "../views/Team/TeamChooseClus";
//问卷调查
import questionIndex from "../views/Question/questionIndex";
import questionJkpg from "../views/Question/questionJkpg";
import questionords from "../views/Question/questionords";
import questionDetails from "../views/Question/questionDetails";
import questionorders from "../views/Question/questionorders";
import questionInput from "../views/Question/questionInput";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "index",
    component: Index,
    meta: {
      Auth: false
    }
  },
  {
    path: "/question",
    name: "question",
    component: question,
    meta: {
      Auth: false
    }
  },
  {
    path: "/jkglUrl",
    name: "jkglUrl",
    component: jkglUrl,
    meta: {
      Auth: true
    }
  },
  {
    path: "/test",
    name: "test",
    component: test,
    meta: {
      index: 3
    }
  },
  {
    path: "/notice",
    name: "notice",
    component: notice,
    meta: {
      index: 1
    }
  },
  {
    path: "/addClusItem",
    name: "addClusItem",
    component: addClusItem,
    meta: {
      index: 2
    }
  },
  {
    path: "/PersonIndex",
    name: "PersonIndex",
    component: PersonIndex,
    meta: {
      index: "1",
      Auth: false
    }
  },
  {
    path: "/VehicleIndex",
    name: "VehicleIndex",
    component: VehicleIndex,
    meta: {
      index: "6",
      Auth: false
    }
  },
  {
    path: "/VehicleIndexs",
    name: "VehicleIndexs",
    component: VehicleIndexs,
    meta: {
      index: "8",
      Auth: false
    }
  },
  {
    path: "/StaffIndex",
    name: "StaffIndex",
    component: StaffIndex,
    meta: {
      index: "7",
      Auth: false
    }
  },
  {
    path: "/PersonalAppointment",
    name: "PersonalAppointment",
    component: PersonalAppointment,
    meta: {
      index: "9",
      Auth: true
    }
  },
  {
    path: "/PersonDetails",
    name: "PersonDetails",
    component: PersonDetails,
    meta: {
      index: "2",
      Auth: false
    }
  },
  {
    path: "/PersonBookSum",
    name: "PersonBookSum",
    component: PersonBookSum,
    meta: {
      index: "3",
      Auth: true
    }
  },
  {
    path: "/PersonOrders",
    name: "PersonOrders",
    component: PersonOrders,
    meta: {
      index: "4",
      Auth: true
    }
  },
  {
    path: "/PersonPayment",
    name: "PersonPayment",
    component: PersonPayment,
    meta: {
      index: "5",
      Auth: true
    }
  },
  {
    path: "/MyOrderList",
    name: "MyOrderList",
    component: MyOrderList,
    meta: {
      index: "1",
      Auth: true
    }
  },
  {
    path: "/OrderDetails",
    name: "OrderDetails",
    component: OrderDetails,
    meta: {
      index: "2",
      Auth: true
    }
  },
  {
    path: "/PayRecord",
    name: "PayRecord",
    component: PayRecord,
    meta: {
      index: "1",
      Auth: true
    }
  },
  {
    path: "/TeamLogin",
    name: "TeamLogin",
    component: TeamLogin,
    meta: {
      index: "1",
      Auth: true
    }
  },
  {
    path: "/TeamChooseClus",
    name: "TeamChooseClus",
    component: TeamChooseClus,
    meta: {
      index: "2",
      Auth: true
    }
  },
  {
    path: "/TeamAddClusItem",
    name: "TeamAddClusItem",
    component: TeamAddClusItem,
    meta: {
      index: "2",
      Auth: true
    }
  },
  {
    path: "/TeamBookSum",
    name: "TeamBookSum",
    component: TeamBookSum,
    meta: {
      index: "3",
      Auth: true
    }
  },
  {
    path: "/TeamDetails",
    name: "TeamDetails",
    component: TeamDetails,
    meta: {
      index: "4"
    }
  },
  {
    path: "/TeamOrders",
    name: "TeamOrders",
    component: TeamOrders,
    meta: {
      index: "5",
      Auth: true
    }
  },
  {
    path: "/ReportLogin",
    name: "ReportLogin",
    component: ReportLogin,
    meta: {
      index: "1",
      Auth: false
    }
  },
  // {
  //   path:'/ReportDataList',
  //   name:'ReportDataList',
  //   component:ReportDataList,
  //   meta:{
  //     index:'2'
  //   }
  // },
  // {
  //   path:'/ReportDetails',
  //   name:'ReportDetails',
  //   component:ReportDetails,
  //   meta:{
  //     index:'3',
  //     Auth:true
  //   }
  // },
  {
    path: "/oauth",
    name: "oauth",
    component: oauth,
    meta: {
      allowBack: false
    }
  },
  // {
  //   path:'/login',
  //   name:'login',
  //   component:login,
  //   meta:{
  //     allowBack: false
  //   }
  // },
  {
    path: "/ReportList",
    name: "ReportList",
    component: ReportList,
    meta: {
      index: 2
    }
  },
  {
    path: "/ReportMian",
    name: "ReportMian",
    component: ReportMian,
    meta: {
      index: 3,
      Auth: false
    }
  },
  {
    path: "/ReportFinal",
    name: "ReportFinal",
    component: ReportFinal,
    meta: {
      index: 4
    }
  },
  {
    path: "/ReportGuide",
    name: "ReportGuide",
    component: ReportGuide,
    meta: {
      index: 5
    }
  },
  {
    path: "/ReportSuggest",
    name: "ReportSuggest",
    component: ReportSuggest,
    meta: {
      index: 6
    }
  },
  {
    path: "/ReportDetails",
    name: "ReportDetails",
    component: ReportDetails,
    meta: {
      index: 6
    }
  },
  {
    path: "/SweepCodePay",
    name: "SweepCodePay",
    component: SweepCodePay,
    meta: {
      index: 1,
      Auth: true
    }
  },
  {
    path: "/SweepOtherPay",
    name: "SweepOtherPay",
    component: SweepOtherPay,
    meta: {
      index: 1,
      Auth: true
    }
  },
  {
    path: "/questionIndex",
    name: "questionIndex",
    component: questionIndex,
    meta: {
      index: 1,
      Auth: true
    }
  },
  {
    path: "/questionJkpg",
    name: "questionJkpg",
    component: questionJkpg,
    meta: {
      index: 2
    }
  },
  {
    path: "/questionords",
    name: "questionords",
    component: questionords,
    meta: {
      index: 3
    }
  },
  {
    path: "/questionDetails",
    name: "questionDetails",
    component: questionDetails,
    meta: {
      index: 4,
      Auth: true
    }
  },
  {
    path: "/questionInput",
    name: "questionInput",
    component: questionInput,
    meta: {
      index: 5,
      Auth: false
    }
  }
];

const router = new VueRouter({
  mode: "hash",
  routes
});

router.beforeEach((to, from, next) => {
  // 强制给index.html 加上版本
  if (
    document.URL.indexOf("?v=" + Vue.prototype.baseData.version) < 0 &&
    (Vue.prototype.baseData.version || "") != ""
  ) {
    window.location.href =
      "?v=" + Vue.prototype.baseData.version + "#" + to.fullPath;
    // console.log(pwindow.location.athname+'?v=' + Vue.prototype.baseData.version + '#' + to.fullPath);
    return;
  }

  if (to.meta.Auth) {
    if (storage.cookie.get("user") != null) {
      next();
    } else {
      storage.session.set("redirect", to.fullPath);
      next({
        path: "/oauth",
        query: {
          type: "jump"
        }
      });
    }
  } else {
    if (storage.cookie.get("isOauth") == 1) {
      storage.cookie.set("isOauth", "0");
      next({ path: storage.session.get("redirect") });
    }
    next();
  }
});
export default router;
