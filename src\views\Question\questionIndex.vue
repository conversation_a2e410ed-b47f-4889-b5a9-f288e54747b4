<template>
  <div>
    <div class="qtitle">
      <div style="background: #fdfcfe;">
        <div class="questBtn" style="">
          <img src="../../assets/question/dc_pic.png" style="width: .95rem;" />
          <span style="margin-left: .1rem;">健康调查</span>
        </div>
      </div>
      <div style="width: 100%;background: azure;margin-top: 0.1rem;">
        <div class="questionmation">
          <div class="questionBox">
            <div class="questionDiv">
              <div class="questionTitle">
                <b>基本信息填写</b>
              </div>
              <div class="questionInput"></div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>姓名</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="请输入姓名" v-model="InputNames" maxlength="6" />
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>手机号</span>
              </div>
              <div class="questionInput">
                <input type="tel" placeholder="请输入手机号" v-model="InputTels" maxlength="11" />
              </div>
            </div>
            <div class="questionDiv" v-if="teamLoginType == '1'">
              <div class="questionTitle">
                <span>身份证号码</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="请输入身份证件号码" v-model="InputCards" maxlength="18" />
              </div>
            </div>
            <div class="questionDiv" v-else>
              <div class="questionTitle">
                <span>身份证、护照</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="请输入证件号码" v-model="InputCards" maxlength="18"
                  @input="getIdCardSex(InputCards)" />
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>年龄</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="请输入年龄" v-model="age" maxlength="11" />
              </div>
            </div>
            <div class="questionDiv" v-if="teamLoginType != '1'">
              <div class="questionTitle">
                <span>性别</span>
              </div>
              <!-- <input type="text" placeholder="请输入与身份证一致的姓名" v-model="InputNames" maxlength="6" /> -->
              <div class="radioGroup">
                <van-radio-group v-model="Sex" direction="horizontal" @change="Sexconfim">
                  <van-radio name="1">男</van-radio>
                  <van-radio name="2">女</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>婚史</span>
              </div>
              <!-- <input type="text" placeholder="请输入与身份证一致的姓名" v-model="InputNames" maxlength="6" /> -->
              <div class="radioGroup">
                <van-radio-group v-model="radio" direction="horizontal" @change="confim">
                  <van-radio name="1">未婚</van-radio>
                  <van-radio name="2">已婚</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>身高（cm）</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="需填写" v-model="shengao" maxlength="11" />
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>体重(kg)</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="需填写" v-model="tiz" maxlength="6" />
              </div>
            </div>
            <!-- <div class="questionDiv">
              <div class="questionTitle">
                <span>血型</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="（知道的可填写）" v-model="xueing" maxlength="11" />
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>腹围</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="（知道的可填写）" v-model="fuwei" maxlength="11" />
              </div>
            </div> -->
            <div class="questionDiv">
              <div class="questionTitle">
                <span>体重指数</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="（根据身高体重自动生成）" v-model="zhishu" maxlength="11" readonly />
              </div>
            </div>
          </div>
        </div>
        <div class="ljxiaybu">
          <div class="btnclik" @click="clicknext">
            <span>下一步</span>

          </div>
        </div>
        <div class="footDiv"></div>
      </div>


    </div>
  </div>
</template>
<script>
import { RadioGroup, Radio, Toast, Dialog } from "vant";
import apiUrils from '../../config/apiUrls'
import { ajax, storage, dataUtils } from '../../common';
export default {
  data() {
    return {
      InputCards: "",
      InputNames: "",
      InputTels: "",
      age: "",
      shengao: "",
      tiz: "",
      xueing: "",
      fuwei: "",
      zhishu: '',
      radio: "",
      type: "",
      Sex: "",
      teamLoginType: "",
    };
  },
  created() {
    // this.type = storage.session.get("type");
    this.type =  this.$route.query["pageType"];
    // if(this.type=="group"){
    //   var user=JSON.parse(storage.session.get("teamUserInfo"));
    //   this.InputCards=user.idcard;
    //   this.InputNames=user.pat_name;
    //   this.teamLoginType=user.teamLoginType;
    //   if(user.teamLoginType=="1"){
    //     this.age=dataUtils.getAge(this.InputCards);
    //   }
    // }
    // else{
    //   var user=JSON.parse(storage.session.get("tjinfos"));
    //   this.InputCards=user.idCard;
    //   this.InputNames=user.name;     
    //   if(this.InputCards.length===18 || this.InputCards.length===15){
    //     this.teamLoginType="1";
    //     this.age=dataUtils.getAge(this.InputCards);
    //   }
    // }
  },
  watch: {
    shengao(val) {
      if (this.tiz == "" && this.tiz == null) {
        return;
      }
      var num = (this.shengao / 100) * (this.shengao / 100);
      var mun = (this.tiz / num).toFixed(2);
      if (isNaN(mun)) {
        this.zhishu = 0;
        return;
      }
      this.zhishu = mun;
    },
    tiz(val) {
      if (this.shengao == "" && this.shengao == null) {
        return;
      }
      var num = (this.shengao / 100) * (this.shengao / 100);
      var mun = (this.tiz / num).toFixed(2);
      if (isNaN(mun)) {
        this.zhishu = 0;
        return;
      }
      this.zhishu = mun;
    }
  },
  methods: {
    confim(e) {
      this.radio = e;
    },
    Sexconfim(e) {
      this.Sex = e;
    },
    //LQ自动填充年龄性别

    getIdCardSex(value) {
      if (value.length == 18) {
        //年龄
        if (dataUtils.isCardID(this.InputCards) != true) {
          Toast(dataUtils.isCardID(this.InputCards));
          return;
        }
        var strBirthday =
          this.InputCards.substr(6, 4) +
          "/" +
          this.InputCards.substr(10, 2) +
          "/" +
          this.InputCards.substr(12, 2);
        var birthDate = new Date(strBirthday);
        var nowDateTime = new Date();
        var ages = nowDateTime.getFullYear() - birthDate.getFullYear();
        //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
        if (
          nowDateTime.getMonth() < birthDate.getMonth() ||
          (nowDateTime.getMonth() == birthDate.getMonth() &&
            nowDateTime.getDate() < birthDate.getDate())
        ) {
          ages--;
        }
        this.age = ages

        //性别
        var strsex = value.substr(16, 1);
        if (value.length == 18) {
          if (parseInt(strsex) % 2 == 0) {
            //女
            this.Sexconfim('2')

          }
          else {
            //男
            this.Sexconfim('1')
          }
        }
      }
    },


    clicknext() {
      if (this.InputCards == "" || this.InputNames == "" || this.InputTels == "" || this.age == "") {
        alert("请完善信息");
        return;
      }
      if (this.shengao == "" || this.tiz == "" || this.radio == "") {
        alert("请完善信息");
        return;
      }
      if (dataUtils.isTel(this.InputTels) != true) {
        Toast(dataUtils.isTel(this.InputTels));
        return;
      }
      if (dataUtils.isCardID(this.InputCards) != true) {
        Toast(dataUtils.isCardID(this.InputCards));
        return;
      }
      var questionInfo = {
        name: this.InputNames,
        tel: this.InputTels,
        InputCards: this.InputCards,
        age: this.age,
        shengao: this.shengao,
        tiz: this.tiz,
        xueing: this.xueing,
        fuwei: this.fuwei,
        zhishu: this.zhishu,
        radio: this.radio,
        sex: this.Sex
      };
      if (this.type == "question") {
        var pData = {
          sex: this.Sex,
          age: this.age,
          marriage: this.radio
        }
        ajax.post(apiUrils.GetQuestionClusData, pData, { nocrypt: true }).then(r => {
          var data = r.data;
          if (!data.success) {
            Toast("获取数据异常！请稍后再试。");
            return;
          }
          if(!data.returnData||data.returnData=="[]"){
            Toast("暂无推荐套餐！");
            return;
          }
          // console.log("data",data);
          storage.session.set("questionClus", data.returnData);
          storage.session.set("questionInfo", JSON.stringify(questionInfo));
          this.$router.replace({
            path: "/questionJkpg",
            query:{
            pageType:this.type
          }
          });
          return;
        }).catch(e => {
          Toast("获取数据异常！请稍后再试");
          return;
        })
      }
      else {
        storage.session.set("questionInfo", JSON.stringify(questionInfo));
        this.$router.replace({
          path: "/questionJkpg",
          query:{
            pageType:this.type
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
body {
  background: white !important;
}

.van-radio-group {
  display: flex;
}

.van-radio {
  margin-left: 0.2rem;
}

.qtitle {
  width: 100%;
  // height: 1rem;
  // background: wheat;
}

.questionmation {
  width: 100%;
  /* height: 4.35rem; */
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.questionmation .questionBox {
  width: 90%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.questionBox .questionDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  border-top: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.questionBox div:nth-child(1) {
  border: 0;
}

.questionDiv .questionTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.questionDiv .questionInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  margin-right: 0.1rem;
}

.questionInput input {
  width: 100%;
  // height: 100%;
  border: none;
  outline: medium;
  text-align: right;
}

.radioGroup {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 70%;
  height: 100%;

  color: #9b9b9b;
  margin-right: 0.1rem;
}

.ljxiaybu {
  width: 100%;
  height: 2.16rem;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: .24rem;
  background: #ffffff;
  border-top: 1px solid #DFE3E9;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
}

.btnclik {
  color: aliceblue;
  font-size: 16px;
  letter-spacing: 5px;
  background: #79a8ee;
  width: 80%;
  height: 1rem;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.questBtn {
  font-size: .5rem;
  color: #4a4a4a;
  text-align: center;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.3rem;
}</style>