<template>
  <div class="person-index">
    <div class="person-tabs">
      <span
        :class="[OnBtn ? 'active-male' : '', 'tabs-item']"
        @click="switchBtnL"
        >男性贵宾套餐</span
      >
      <span
        :class="[!OnBtn ? 'active-female' : '', 'tabs-item']"
        @click="switchBtnR"
        >女性贵宾套餐</span
      >
    </div>
    <div class="person-content">
      <div
        class="content-item"
        v-for="(item, index) in CardDataList"
        :key="index"
        @click="toDetails(item)"
      >
        <div class="item-title">
          <img
            class="item-img"
            src="../../assets/icon/male.png"
            alt=""
            v-if="sex == '1'"
          />
          <img
            class="item-img"
            src="../../assets/icon/female.png"
            alt=""
            v-if="sex == '2'"
          />
          <span>{{ item.clus_Name }}</span>
        </div>
        <div class="item-price">
          <div class="price-text">¥{{ item.price }}</div>
          <div class="detail-btn">
            <span>查看详情</span>
            <img class="item-img" src="../../assets/icon/next.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <!-- <div id="Personal">
      <div class="provp" v-show="provp">
        <img src="../../assets/clusGif.gif" />
      </div>
      <div class="switchBtn">
        <div :class="[OnBtn ? 'BtnBlueL' : 'BtnWhiteL']" @click="switchBtnL">
          男性贵宾套餐
        </div>
        <div :class="[!OnBtn ? 'BtnBlueR' : 'BtnWhiteR']" @click="switchBtnR">
          女性贵宾套餐
        </div>
      </div>
      <div
        class="PersonalCard top"
        v-for="(item, index) in CardDataList"
        :key="index"
        @click="toDetails(item)"
      >
        <div class="CardTitle">
          <div class="titleImg" v-if="sex == '1'">
            <img src="../../assets/detailsMan01.png" alt="套餐详情" />
          </div>
          <div class="titleImg" v-if="sex == '2'">
            <img src="../../assets/detailsWoman01.png" alt="套餐详情" />
          </div> -->
    <!--<div class="titleImg">
                    <img src="../../assets/detailsSex.png" alt="套餐详情">
          </div>-->
    <!-- <div class="CardText">
            <span>{{ item.clus_Name }}</span>
          </div>

          <div class="CardSee">
            <span>查看详情</span>
            <span>
              <img src="../../assets/Nextpage.png" alt="查看详情" />
            </span> -->
    <!-- <div class="CardSeeImg"><img src="./picture/Nextpage.png" alt="查看详情"></div> -->
    <!-- </div>
        </div>
        <div class="PersonalSpan">
          <span>{{ item.clus_Note }}</span>
        </div>
        <div class="price">
          <span>￥</span>
          <span>{{ item.price }}</span>
        </div>
      </div>
      <div class="footDiv"></div> -->
    <div v-show="CardDataList.length == 0" class="clusFlag">
      <div>
        <img src="../../assets/kong.png" />
      </div>
      <span style="margin-top: 10px; color: dimgray"
        >暂无体检套餐，请联系管理员</span
      >
    </div>
    <!-- </div> -->
    <van-dialog
      v-model="showTip"
      title="温馨提示"
      confirmButtonText="我已了解该套餐注意事项"
      @confirm="isCancleTip()"
    >
      <div class="tipdialog_container" v-html="defaulDialogText"></div>
    </van-dialog>
  </div>
</template>
<script>
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      OnBtn: true, //切换class
      sex: 1, // 1 男 2女  3不限性别
      CardDataList: [], //筛选后数据
      CardData: [],
      manCluster: [],
      womanCluster: [],
      clusFlag: false,
      provp: true,
      type: "",
      iimgs: require("../../assets/detailsWoman01.png"),
      showTip: false,
      defaulDialogText:
        "(一)注意事项:" +
        "<p>1.请携带身份证按预约日期进行体检，体检前三天请您保持正常饮食，勿进食过于油腻、高蛋白食品，晨起禁食，保持空腹8-14小时，不要饮酒，避免剧烈运动。</p>" +
        "<p>2.体检当日需空腹进行采血、肝、胆、脾、胰彩超以及尿素呼气试验（C13、C14）等项目检查，待上述检查完毕后，方可进食。</p>" +
        "<p>3.泌尿系（双肾、膀胱、输尿管）彩超，妇科彩超（经腹）体检前，需膀胱充盈后方可进行。</p>" +
        "<p>4.糖尿病、高血压、心脏病、哮喘等慢性病患者，体检当日请照常服药。</p>" +
        "<p>5.体检当日着装宜简单、宽松、舒适；勿穿连衣裙、连裤袜，避免穿有金属、纽扣、拉链、珠片、胶印花图案的衣服，以方便检查，长发盘起，勿化妆及佩戴首饰等（X光检查前应取下佩戴的金属性物品，如有金属袖扣的衣服、内衣、项链、手机、钢笔、钥匙等）；请提前到更衣室换衣服，并妥善保管。</p>" +
        "<p>6.请告知医生病史，尤其是重要疾病史，它是体检医生判断体检者健康状况的重要参考依据。</p>" +
        "<p>7.进行各项检查时，务必按预定项目逐科、逐项检查，不要漏检，以免影响最后的总检结论。</p>" +
        "<p>8.如有不适请到相关专业科室就诊，由专业医生予以诊疗，以免延误病情。</p>" +
        "(二)女士检查应注意:" +
        "<p>1.妇科检查前3天请勿阴道上药、阴道冲洗及性生活等。</p>" +
        "<p>2.已怀孕或者计划怀孕者，务必检查前告知医护人员，不能做放射类、尿素呼气试验等检查，请到现场与医生沟通后再开单缴费检查。</p>" +
        "<p>3.从无性生活者禁做妇科检查、阴道彩超检查，仅限于已婚或有性生活史者。</p>" +
        "<p>4.月经期间请勿留取尿液。</p>" +
        "<p>5.妇科检查、宫颈涂片、液基细胞薄层制片（TCT）避开月经期检查，待月经干净3天后再补检。</p>"
    };
  },
  created() {
    this.type = storage.session.get("type");
    this.getClusterDetails();
  },
  methods: {
    getClusterDetails() {
      var that = this;
      var pData = {
        clus_type: "3"
      };
      ajax.post(apiUrls.GetStaffClusItem, pData, { nocrypt: true }).then(r => {
        if (r.data.success) {
          var data = r.data.returnData;
          that.manCluster = data.m;
          that.womanCluster = data.w;
          that.CardDataList = that.manCluster;
        }
        that.provp = false;
      });
    },
    switchBtnL() {
      this.OnBtn = true;
      this.sex = 1;
      this.CardDataList = this.manCluster;
      if (this.CardDataList == null) {
        this.clusFlag = true;
      } else {
        this.clusFlag = false;
      }
    },

    switchBtnR() {
      this.OnBtn = false;
      this.sex = 2;
      this.CardDataList = this.womanCluster;
      if (this.CardDataList == null) {
        this.clusFlag = true;
      } else {
        this.clusFlag = false;
      }
    },
    toDetails(item) {
      var dataList = {
        clus_Code: item.clus_Code,
        sex: this.sex,
        clus_Name: item.clus_Name,
        price: item.price,
        PersonalSpan: item.clus_Note
      };
      window.sessionStorage.setItem("dataList", JSON.stringify(dataList));
      // this.showTip = true;
      this.isCancleTip();
    },
    isCancleTip() {
      this.showTip = false;
      this.$router.push({
        path: "/PersonDetails"
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.person-index {
  margin: 0.21rem;
  .person-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 0.2rem;
    .tabs-item {
      background: #fff;
      font-size: 0.28rem;
      color: #999999;
      letter-spacing: -0.34px;
      padding: 0.2rem 0.94rem;
      &.active-male {
        background: #1678ff;
        color: #fff;
      }
      &.active-female {
        background: #fc677d;
        color: #fff;
      }
    }
  }
  .person-content {
    display: grid;
    gap: 0.21rem;
    .content-item {
      background: #fff;
      padding: 0.22rem;
      box-shadow: 0px 2px 4px 0px rgba(153, 153, 153, 0.5);
      border-radius: 0.08rem;
      display: grid;
      grid-template-rows: 1fr 1fr;
      gap: 0.1rem;
      .item-title {
        display: flex;
        align-items: center;
        gap: 0.18rem;
        font-size: 0.28rem;
        color: #4a4a4a;
        letter-spacing: -0.34px;
      }
      .item-img {
        width: 0.28rem;
      }
      .item-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .price-text {
          font-size: 0.25rem;
          color: #ff0026;
          letter-spacing: -0.3px;
          font-weight: 500;
        }
        .detail-btn {
          display: flex;
          align-items: center;
          font-size: 0.25rem;
          color: #1678ff;
          letter-spacing: -0.3px;
          gap: 0.08rem;
        }
      }
    }
  }
}
.bt_common {
  width: 100%;
  /* text-align: center; */
  justify-content: center;
  align-items: center;
  display: flex;
  background-color: #3e89f9;
  color: #f7f7f7;
  font-stretch: 46px;
  letter-spacing: 4px;
}
.switchBtn {
  width: 92%;
  height: 0.8rem;
  font-size: 0.3rem;
  display: flex;
  margin: 0.24rem auto;
  background: white;
  font-weight: bold;
}

.switchBtn .BtnBlueL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 2px 0 0 2px;
}

.switchBtn .BtnWhiteL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 2px 0 0 2px;
}

.switchBtn .BtnBlueR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ff7e98;
  color: white;
  border-radius: 0 2px 2px 0;
}

.switchBtn .BtnWhiteR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #ff7e98;
  border: 1px solid #ff7e98;
  border-radius: 0 2px 2px 0;
}

.PersonalCard {
  width: 94%;
  height: 2.72rem;
  margin: 0.08rem auto;
  background: white;
  font-size: 0.28rem;
  border-radius: 0.1rem;
  -moz-box-shadow: 2px 2px 5px #333333;
  -webkit-box-shadow: 2px 2px 5px #333333;
  box-shadow: 2px 2px 5px #333333;
}

.top {
  margin-top: 0.24rem;
}

.PersonalCard .CardTitle {
  width: 96%;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 4%;
}

.CardTitle .titleImg {
  width: 0.56rem;
  height: 0.56rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.CardText {
  width: calc(100% - 2.52rem);
  height: 1rem;
  display: flex;
  align-items: center;
  /* margin-left: .2rem; */
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  font-weight: 600;
}

.CardText span {
  margin-left: 0.2rem;
}

.CardSee {
  width: 1.96rem;
  height: 1rem;
  color: #6a9be4;
  letter-spacing: -0.01px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.CardSeeImg {
  width: 0.64rem;
  height: 0.64rem;
}

.icon-Nextpage {
  /*margin-top: 3px;*/
  font-size: 0.5rem;
}

.PersonalCard .PersonalSpan {
  width: 92%;
  height: 0.88rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin: 0 auto;
  /* 超过两行省略号 */
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.PersonalCard .price {
  width: 92%;
  margin: 0.08rem auto;
  font-size: 0.36rem;
  color: #6a9be4;
  letter-spacing: -0.02px;
  display: flex;
  align-items: center;
}

.price span:nth-child(1) {
  font-size: 0.24rem;
}

#Personal .footDiv {
  width: 100%;
  height: 0.5rem;
}
.provp img {
  width: 100%;
  position: fixed;
  z-index: 2006;
}
.provp {
  position: fixed;
  top: 0;
  left: 0;
  background: #fff;
  width: 100vw;
  height: 100vh;
  z-index: 2005;
  opacity: 0.75;
  display: flex;
  justify-content: center;
  align-items: center;
}
.clusFlag {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.3rem;
}
.tipdialog_container {
  width: 90%;
  margin: 0 auto;
  max-height: 60vh;
  overflow-y: auto;
}
.tipdialog_container::-webkit-scrollbar {
  width: 0;
}

.tipdialog_container .dialog1_content .van-checkbox {
  align-items: start !important;
}

::v-deep .van-dialog__header {
  font-size: 0.4rem;
  font-weight: 800;
}

::v-deep .van-button--large {
  letter-spacing: 1px;
}
</style>
