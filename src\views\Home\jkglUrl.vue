<template>
  <div>
    <!-- 遮罩层\加载 -->
    <van-overlay :show="showOvly" v-show="showOvly">
      <!-- <div class="vanoverBtn"> -->
      <van-loading
        type="spinner"
        color="#fff"
        vertical
        size="1rem"
        text-size="0.3rem"
        >授权中...</van-loading
      >
      <!-- </div> -->
    </van-overlay>
  </div>
</template>
<script>
import Vue from "vue";
import { storage, toolsUtils } from "../../common";
import { Toast } from "vant";
export default {
  data() {
    return {
      showOvly: true,
      jkglUrl: Vue.prototype.baseData.jkglUrl,
    };
  },
  created() {
    this.toJkoauth();
  },
  methods: {
    toJkoauth() {
      let openid = storage.cookie.get("openid");
      if (openid) {
        // console.log("toolsUtils.OpenEnc(openid)", toolsUtils.OpenEnc(openid));
        // return;
        window.location.href =
          this.jkglUrl + encodeURIComponent(toolsUtils.OpenEnc(openid));
        return;
      } else {
        Toast("获取授权失败！");
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
