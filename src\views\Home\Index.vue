<template>
  <div class="home">
    <!-- 轮播图 -->
    <div class="carousel">
      <van-swipe :autoplay="5000" indicator-color="white">
        <van-swipe-item v-for="(item, index) in carouselList" :key="index">
          <img :src="item.imgUrl" alt />
        </van-swipe-item>
      </van-swipe>
    </div>
    <div class="examination-wrap">
      <div class="examination-card" @click="toPersonal">
        <div class="card-wrap personal">
          <div class="card-title">个人体检预约</div>
          <div class="card-desc">用于个人健康体检</div>
        </div>
      </div>
      <div class="examination-card" @click="toTeamTest">
        <div class="card-wrap team">
          <div class="card-title">团体体检预约</div>
          <div class="card-desc">用于公司团队体检</div>
        </div>
      </div>
    </div>
    <div class="individual-center">
      <div>个人中心</div>
      <div class="option-wrap">
        <div class="center-item reserve" @click="toMyAppointment">
          <img src="../../assets/icon/reserve.png" alt="" />
          <div>预约查询</div>
        </div>
        <div class="center-item report" @click="MyReport">
          <img src="../../assets/icon/report.png" alt="" />
          <div>体检报告</div>
        </div>
        <div class="center-item guidedInspection" @click="onGuidedInspection">
          <img src="../../assets/icon/guidedInspection.png" alt="" />
          <div>科室导航</div>
        </div>
        <div class="center-item healthManage" @click="toPayRecord">
          <img src="../../assets/icon/healthManage.png" alt="" />
          <div>健康管理</div>
        </div>
      </div>
    </div>
    <div class="notice" @click="Notice">
      <div class="notice-text">
        <div class="notice-title">
          <span>体检须知</span>
          <span class="title-text">注意查看</span>
        </div>
        <div class="notice-content">
          尊敬的体检客户：您好！欢迎选择我院进行年度健康体检，为了更顺利地进行体检，请您认真阅读以下注意事项：
          <br />
          体检时间：具体请按单位安排时间、批次进行参检，当天抽血时间截至到10：00
        </div>
      </div>
      <img src="../../assets/icon/notice.png" alt="" />
    </div>
    <div class="logo">
      <img class="logo-img" :src="`${publicPath}img/HomeLogo.jpg`" alt="" />
      <div class="logo-title">{{ baseData.hospitalName }}</div>
      <div class="logo-level">{{ baseData.hospitalTitle }}</div>
      <div class="logo-address">
        <img src="../../assets/address.png" alt="" />
        <span>{{ baseData.hospitalAddress }}</span>
      </div>
      <div class="logo-tel">
        <img src="../../assets/icon/telephone.png" alt="" />
        <span>{{ baseData.hospitalTel1 }}</span>
      </div>
      <div class="logo-tel">
        <img src="../../assets/icon/telephone.png" alt="" />
        <span>{{ baseData.hospitalTel2 }}</span>
      </div>
      <div class="logo-tel">
        <img src="../../assets/icon/telephone.png" alt="" />
        <span>{{ baseData.hospitalTel3 }}</span>
      </div>
    </div>
    <!-- 轮播图 -->
    <!-- <van-swipe :autoplay="3000" indicator-color="white" > -->
    <!-- <div class="topImg">
      <van-swipe :autoplay="5000" indicator-color="white">
        <van-swipe-item>
          <img src="../../assets/icon/1.jpg" alt />
        </van-swipe-item>
        <van-swipe-item>
          <img src="../../assets/icon/2.jpg" alt />
        </van-swipe-item>
        <van-swipe-item>
          <img src="../../assets/icon/3.jpg" alt />
        </van-swipe-item>
      </van-swipe>
    </div> -->
    <!-- 体检类别 -->
    <!-- <div class="test"> -->
    <!-- 类别按钮 -->
    <!-- <div class="category" style="height: 3.2rem; background: #efefefe8">
        <div class="categoryTitle" style="height: 1rem"> -->
    <!-- <div class="blackBox"></div> -->
    <!-- <span>体检类别</span> -->
    <!-- </div> -->
    <!-- <div class="categoryBtn">
          <div class="SmallBox" @click="toPersonal">
            <div class="SmallText">
              <span>个人体检</span>
              <span>用于个人健康体检</span>
            </div>
            <div class="SmallImg">
              <img src="../../assets/icon/nurse.png" alt="个人体检" />
            </div>
          </div>
          <div class="SmallBoxL" @click="toTeamTest">
            <div class="SmallText">
              <span>团体体检</span>
              <span>用于公司团队体检</span>
            </div>
            <div class="SmallImg">
              <img src="../../assets/icon/doctor.png" alt="团队体检" />
            </div>
          </div>
        </div>
      </div> -->

    <!-- 个人中心 -->

    <!-- 类别按钮 -->
    <!-- <div class="category">
        <div class="categoryTitle">
          <div class="blackBox"></div>
          <span>个人中心</span>
        </div>
        <div class="BtnIcon">
          <div @click="toMyAppointment">
            <img src="../../assets/icon/iconOrder.png" alt="我的预约" />
            <span>我的预约</span>
          </div>
          <div></div>
          <div @click="MyReport">
            <img src="../../assets/icon/iconReport.png" alt="我的报告" />
            <span>我的报告</span>
          </div>
        </div>

        <div class="BtnIcon">
          <div @click="toPayRecord">
            <img src="../../assets/PayRecord.png" alt="健康管理" />
            <span>健康管理</span>
          </div>
          <div></div>
          <div @click="toQuestion">
            <img src="../../assets/qiye.png" alt="我的报告" />
            <span>问卷推荐</span>
          </div>
          <div></div>
        </div>
      </div>
    </div> -->

    <!-- 底部 -->
    <!-- <div class="LogoFoot">
      <img
        :src="`${publicPath}img/HomeLogo.jpg`"
        alt="医院Logo"
        class="LogoImg"
      />
      <span class="LogoTitle">{{ baseData.hospitalName }}</span>
      <span class="LogoText">{{ baseData.hospitalTitle }}</span>
      <div class="address">
        <img src="../../assets/address.png" alt="地址" />
        <span>{{ baseData.hospitalAddress }}</span>
      </div>
      <div class="phone">
        <img src="../../assets/phone.png" alt="联系方式" />
        <span>{{ baseData.hospitalTel }}</span>
      </div>
    </div> -->
    <!-- 体检须知 -->
    <!-- <div class="Notice" @click="Notice">
      <div class="Notice1">
        <img src="../../assets/icon/iconNotice.png" alt />
        <div class="NoticeSpan">
          <span>体检</span>
          <span>须知</span>
        </div>
      </div>
      <div class="Notice2">
        <span
          >尊敬的体检客户：您好！欢迎选择我院进行年度健康体检，为了更顺利地进行体检，请您认真阅读以下注意事项：</span
        >
        <br />
        <div>
          体检时间：具体请按单位安排时间、批次进行参检，当天抽血时间截至到10：00
        </div>
      </div>
    </div> -->
    <!--弹窗选择-->
    <van-popup v-model="show" style="padding-bottom: 6px">
      <div style="width: 80%; margin: 0 auto 6px; height: 25 px">
        根据需要，请选择：
      </div>
      <div class="optional" @click="PersonCommon('person')">
        <div class="popcom">
          <img src="../../assets/icon/iconoptional.png" alt="个人健康体检" />
          <span>个人健康体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>
      <div class="pestaff" @click="PersonCommon('staff')">
        <div class="popcom">
          <img src="../../assets/icon/iconstaff.png" alt="专科体检" />
          <span>专科体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>
      <div class="pecommon" @click="PersonCommon('vehicle')">
        <div class="popcom">
          <img src="../../assets/icon/iconvehicle.png" alt="贵宾体检" />
          <span>贵宾体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>
      <div class="pecommon" @click="toAddItem('person')">
        <div class="popcom">
          <img src="../../assets/icon/iconvehicle.png" alt="贵宾体检" />
          <span>自选项目</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>

      <!-- <div class="question" @click="PersonCommon('question')">
        <div class="popcom">
          <img src="../../assets/icon/iconquestion.png" alt="调查问卷" />
          <span>问卷推荐</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>-->
      <!-- <div class="pestaff" @click="PersonCommon('staff')">
        <div class="popcom">
          <img src="../../assets/icon/iconstaff.png" alt=" 职业健康体检" />
          <span> 职业健康体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>-->
      <!-- <div style="margin:3px "/>
      <div class="optional" @click="PersonCommon('person')">
        <div class="popcom">
          <img src="../../assets/icon/iconoptional.png" alt="普通健康体检" />
          <span>普通健康体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>-->
    </van-popup>
    <van-dialog
      v-model="showTip"
      title="温馨提示"
      @confirm="isCancleTip()"
      confirmButtonText="已阅"
    >
      <div class="tipdialog_container" v-html="defaulDialogText"></div>
    </van-dialog>
    <van-popup
      v-model="showGuidedInspection"
      :close-on-click-overlay="false"
      closeable
    >
      <img src="../../assets/icon/applet-code.png" alt="" />
    </van-popup>
  </div>
</template>
<script>
import { Toast } from "vant";
import Vue from "vue";
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      carouselList: [
        { imgUrl: require("../../assets/icon/carousel1.png") },
        { imgUrl: require("../../assets/icon/carousel2.png") },
        { imgUrl: require("../../assets/icon/carousel3.png") },
        { imgUrl: require("../../assets/icon/carousel4.png") },
        { imgUrl: require("../../assets/icon/carousel5.png") },
        { imgUrl: require("../../assets/icon/carousel6.png") },
        { imgUrl: require("../../assets/icon/carousel7.png") },
        { imgUrl: require("../../assets/icon/carousel8.png") },
      ],
      openid: "",
      show: false,
      publicPath: process.env.BASE_URL,
      baseData: Vue.prototype.baseData,
      showTip: false,
      defaulDialogText: Vue.prototype.baseData.defaulDialogText,
      showGuidedInspection: false,
    };
  },
  created() {
    storage.session.delete("dataList");
    storage.session.delete("chooseItem");
    storage.session.delete("clusIncludeItem");
    storage.session.delete("QuestionAnswer");
    storage.session.delete("questionClus");
    storage.session.delete("questionInfo");
    storage.session.delete("spTypeQ");
    storage.session.delete("question");
    storage.session.delete("onlyClus");
  },
  // created() {
  //   this.openid = this.$route.query["openId"];
  //   var lq = storage.cookie.get("openid");
  //   if (this.openid == null || this.openid == "" || this.openid == undefined) {
  //     if (lq == null || lq == "" || lq == undefined) {
  //       alert("请从微信公众号进入");
  //       window.close();
  //       WeixinJSBridge.call("closeWindow");
  //       return;
  //     }
  //   } else {
  //     storage.cookie.set("openid", this.openid);
  //     window.location.replace(Vue.prototype.baseData.apiHost);
  //     return;
  //   }
  // },
  methods: {
    //弹出显示
    toPersonal() {
      // Toast("暂未开放");
      // return;
      // this.show = true;
      this.$router.push({
        path: "/PersonalAppointment",
      });
    },
    isCancleTip() {
      this.showTip = false;
      let type = storage.session.get("type");
      switch (type) {
        case "person":
          this.$router.push({
            path: "/PersonIndex",
          });
          break;
        case "vehicle":
          this.$router.push({
            path: "/VehicleIndexs",
          });
          break;
        case "staff":
          this.$router.push({
            path: "/StaffIndex",
          });
          break;
      }
    },
    //个人体检选择
    PersonCommon(type) {
      // this.showTip = true;
      storage.session.set("type", type);
      this.isCancleTip();
    },
    toAddItem(type) {
      // storage.session.delete("dataList")
      storage.session.set("type", type);
      this.$router.push({
        path: "/addClusItem",
      });
    },
    //团体体检
    toTeamTest() {
      // Toast("暂未开放");
      // return;
      this.$router.push({
        path: "/TeamLogin",
      });
    },
    //我的预约
    toMyAppointment() {
      // Toast("暂未开放");
      // return;
      this.$router.push({
        path: "/MyOrderList",
      });
    },
    //我的报告
    MyReport() {
      // Toast("暂未开放");
      // return;
      //window.location.href = "https://nbzqzyy.1451cn.com/nztj/#/ReportLogin";
      this.$router.push({
        path: "/ReportLogin",
      });
    },
    onGuidedInspection() {
      this.showGuidedInspection = true;
    },
    toPayRecord() {
      this.$router.push({
        path: "/jkglUrl",
      });
    },
    toQuestion() {
      Toast("暂未开放");
      return;
    },
    //体检须知
    Notice() {
      // window.location.href = "https://nbzqzyy.1451cn.com/nztj/reminder.pdf";
      // return;
      this.$router.push({
        path: "/notice",
      });
    },
    //微信支付测试
    // ssss() {
    //   var pData = { code: "" };
    //   ajax
    //     .post(apiUrls.PersonOrderPay, pData, { nocrypt: true })
    //     .then(r => {
    //       var data = r.data.returnData;
    //       alert(JSON.stringify(data));
    //       WeixinJSBridge.invoke(
    //         "getBrandWCPayRequest",
    //         {
    //           appId: data.appid, //公众号名称，由商户传入
    //           timeStamp: data.timeStamp, //时间戳
    //           nonceStr: data.nonceStr, //随机串
    //           package: data.package, //扩展包
    //           signType: "MD5", //微信签名方式:MD5
    //           paySign: data.paySign //微信签名
    //         },
    //         function(res) {
    //           alert(JSON.stringify(res));

    //           if (res.err_msg == "get_brand_wcpay_request:ok") {
    //             if (confirm("支付成功！点击“确定”进入退款流程测试。")) {
    //             }
    //             //console.log(JSON.stringify(res));
    //           }
    //           // 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
    //           //因此微信团队建议，当收到ok返回时，向商户后台询问是否收到交易成功的通知，若收到通知，前端展示交易成功的界面；若此时未收到通知，商户后台主动调用查询订单接口，查询订单的当前状态，并反馈给前端展示相应的界面。
    //         }
    //       );
    //     })
    //     .catch(e => {
    //       console.log(e);
    //     });
    // }
  },
};
</script>
<style lang="scss" scoped>
.home {
  .carousel {
    img {
      width: 100%;
      height: 100%;
    }
  }
  .examination-wrap {
    display: flex;
    gap: 0.22rem;
    margin: 0.22rem;
    .examination-card {
      font-size: 0.32rem;
      display: flex;
      align-items: center;
      gap: 0.2rem;
      .card-wrap {
        display: flex;
        flex-direction: column;
        gap: 0.08rem;
        letter-spacing: -0.39px;
        padding: 0.22rem;
        width: 2.98rem;
        height: 1.13rem;
        color: #fff;
        border-radius: 0.14rem;
        &.personal {
          background-image: url("../../assets/icon/personal.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
        }
        &.team {
          background-image: url("../../assets/icon/team.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
        }
      }
      .card-title {
        font-weight: 500;
      }
      .card-desc {
        font-size: 0.175rem;
        letter-spacing: -0.24px;
        font-weight: 400;
      }
    }
  }
  .individual-center {
    background: #fff;
    margin: 0 0.22rem;
    font-size: 0.32rem;
    padding: 0.24rem;
    color: #4a4a4a;
    margin-bottom: 0.2rem;
    letter-spacing: -0.39px;
    font-weight: 400;
    border-radius: 0.07rem;
    .option-wrap {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 0.24rem;
      margin-top: 0.24rem;
      .center-item {
        display: flex;
        align-items: center;
        gap: 0.32rem;
        background: #f5f8ff;
        border-radius: 0.16rem;
        padding: 0.36rem 0.4rem;
        color: #000000;
        img {
          width: 0.42rem;
        }
        &.reserve {
          background-image: url("../../assets/icon/reserve-background.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
        }
        &.report {
          background-image: url("../../assets/icon/report-background.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
        }
        &.guidedInspection {
          background-image: url("../../assets/icon/guidedInspection-background.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
        }
        &.healthManage {
          background-image: url("../../assets/icon/healthManage-background.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
        }
      }
    }
  }
  .notice {
    background: #fff;
    margin: 0 0.22rem;
    font-size: 0.32rem;
    padding: 0.14rem 0.42rem 0.14rem 0.22rem;
    display: flex;
    align-items: center;
    gap: 0.34rem;
    margin-bottom: 0.2rem;
    border-radius: 0.07rem;
    .notice-text {
      .notice-title {
        font-size: 0.31rem;
        display: flex;
        gap: 0.18rem;
        letter-spacing: -0.43px;
        font-weight: 500;
        margin-bottom: 0.09rem;
        .title-text {
          color: #1678ff;
        }
      }
      .notice-content {
        font-size: 0.2rem;
        color: #666666;
        letter-spacing: -0.24px;
        font-weight: 400;
      }
    }
    img {
      width: 1.26rem;
      height: 1.22rem;
    }
  }
  .logo {
    background: #fff;
    font-size: 0.28rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.32rem;
    gap: 0.1rem;
    font-weight: 400;
    .logo-img {
      width: 1.28rem;
      height: 1.28rem;
      margin-bottom: 0.1rem;
    }
    .logo-title {
      font-size: 0.32rem;
      color: #4a4a4a;
      letter-spacing: -0.34px;
    }
    .logo-level {
      color: #4a90e2;
      letter-spacing: -0.3px;
    }
    .logo-address {
      display: flex;
      align-items: center;
      gap: 0.1rem;
      color: #9b9b9b;
      letter-spacing: -0.3px;
    }
    .logo-tel {
      display: flex;
      align-items: center;
      gap: 0.16rem;
      color: #4a90e2;
      padding: 0.16rem 1rem;
      outline: 1px solid #4a90e2;
      border-radius: 0.08rem;
      margin-top: 0.1rem;
      letter-spacing: -0.3px;
      img {
        width: 0.32rem;
        height: 0.32rem;
      }
    }
  }
}
.van-swipe {
  height: 4rem !important;
}
.topImg {
  width: 100%;
  height: 4rem;
  img {
    width: 100%;
    height: 100%;
  }
}
/* 体检类别部分 */
.test {
  width: 100%;
  min-height: 6.2rem;
  box-sizing: border-box;
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  background: white;
}

.test .category {
  width: 100%;
  min-height: 3rem;
}

.category .categoryTitle {
  width: 100%;
  height: 0.64rem;
  display: flex;
  align-items: center;
}

.categoryTitle .blackBox {
  width: 0.08rem;
  height: 0.24rem;
  background: #31bfb4;
  margin-left: 0.28rem;
}

.categoryTitle span {
  margin-left: 0.12rem;
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.01px;
  font-weight: 600;
}

.category .categoryBtn {
  width: 100%;
  height: 2rem;
  display: flex;
}

.categoryBtn .SmallBox {
  width: calc((100% - 0.62rem) / 2);
  height: 2rem;
  display: flex;
  background: #daebfc;
  border-radius: 4px;
  margin-left: 0.24rem;
  align-items: flex-end;
}

.categoryBtn .SmallBoxL {
  width: calc((100% - 0.62rem) / 2);
  margin-left: 0.14rem;
  height: 2rem;
  display: flex;
  background: #daebfc;
  border-radius: 4px;
  align-items: flex-end;
}

.categoryBtn .SmallText {
  width: 60%;
  height: 1.84rem;
  display: flex;
  flex-direction: column;
}

.SmallText span:nth-child(1) {
  letter-spacing: -0.02px;
  margin-left: 0.14rem;
  font-weight: 500;
}

.SmallText span:nth-child(2) {
  font-size: 0.18rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  margin-left: 0.14rem;
  margin-top: 0.12rem;
}

.categoryBtn .SmallImg {
  width: 40%;
  height: 1.84rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.SmallImg img {
  width: 1rem;
  height: 1.8rem;
}
.Notice {
  width: 94%;
  height: 1.6rem;
  background: white;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.5);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 3.2rem;
  left: 3%;
  .Notice1 {
    width: 1.2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      width: 32px;
      height: 32px;
    }
    .NoticeSpan {
      width: 100%;
      height: 0.4rem;
      font-size: 0.28rem;
      font-weight: 600;
      margin-top: 3px;
      span:nth-child(1) {
        color: #4a90e2;
      }
      span:nth-child(2) {
        color: #e2d84a;
      }
    }
  }
  .Notice2 {
    width: 5rem;
    height: 1.28rem;
    font-size: 0.24rem;
    margin-left: 10px;
    color: #666666;

    div {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// .Notice div:nth-child(1) {
//   width: 50%;
//   height: 0.84rem;
//   display: flex;
//   align-items: center;
// }

// .Notice div:nth-child(1) img {
//   margin-left: 0.24rem;
// }

// .Notice div:nth-child(1) span {
//   margin-left: 0.12rem;
//   font-size: 0.28rem;
//   letter-spacing: -0.01px;
// }

// .Notice div:nth-child(2) {
//   width: 50%;
//   height: 0.84rem;
//   display: flex;
//   align-items: center;
//   justify-content: flex-end;
//   margin-right: 0.24rem;
// }

// .Notice div:nth-child(2) img {
//   width: 0.16rem;
//   height: 0.32rem;
// }

/* 个人中心 */
.BtnIcon {
  width: 100%;
  height: 2.34rem;
  font-size: 0.32rem;
  letter-spacing: -0.02px;
  display: flex;
  border-top: 0.02rem solid #ebe8e8;
}

.BtnIcon div:nth-child(1) {
  width: calc((100% - 1px) / 2);
  height: 2.34rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.BtnIcon div:nth-child(2) {
  width: 1px;
  height: 1.8rem;
  background: #ebe8e8;
}

.BtnIcon div:nth-child(3) {
  width: calc((100% - 1px) / 2);
  height: 2.34rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.BtnIcon div img {
  width: 1.3rem;
  height: 1.3rem;
}

.BtnIcon div span {
  margin-top: 0.2rem;
}

/* 底部 */
.LogoFoot {
  width: 100%;
  height: 5.14rem;
  background: white;
  margin-top: 10px;
  font-size: 0.32rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  letter-spacing: -0.02px;
  /* margin-bottom: .36rem; */
}
.LogoImg {
  width: 65px;
  height: 65px;
}
.LogoFoot .LogoTitle {
  color: #4a4a4a;
  margin-top: 0.246rem;
}

.LogoFoot .LogoText {
  font-size: 0.28rem;
  margin-top: 0.16rem;
  color: #6a9be4;
  letter-spacing: -0.01px;
}

.address {
  font-size: 0.28rem;
  margin-top: 0.16rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone {
  width: 6.78rem;
  height: 0.96rem;
  border: 0.02rem solid #6a9be4;
  border-radius: 0.1rem;
  margin-top: 0.36rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone span {
  margin-left: 0.16rem;
  color: #6a9be4;
  letter-spacing: -0.02px;
}
//弹窗样式
.van-popup {
  background: #f5f5f5 !important;
  border-radius: 8px;
  height: 6rem !important;
}
.van-overlay {
  background-color: rgba(73, 73, 73, 0.7) !important;
}
.pecommon {
  height: 0.9rem;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px auto 0 !important;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.5);
  border-radius: 4px;
}
.pestaff {
  height: 1rem;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px auto 0 !important;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.5);
  border-radius: 4px;
}
.pecommonImg {
  width: 65%;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pecommonImg img {
  width: 25px;
  height: 25px;
}

.pecommonImg span {
  margin-left: 8%;
}
.nextpage {
  width: 30%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  img {
    width: 18px;
    height: 18px;
  }
}
.optional {
  height: 0.9rem;
  width: 80%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.5);
  border-radius: 4px;
}
.question {
  height: 0.9rem;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px auto 0 !important;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.5);
  border-radius: 4px;
}

.popcom {
  width: 65%;
  height: 30px;
  display: flex;
  //   justify-content: center;
  align-items: center;
  font-size: 0.28rem;
}
.popcom img {
  width: 0.34rem;
  height: 0.34rem;
}
.popcom span {
  margin-left: 8%;
}
.van-popup--center {
  top: 50%;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  width: 80%;
  height: 50%;
  border-radius: 16px;
  z-index: 2002;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 0.28rem;
  color: #4a4a4a;
}
.tipdialog_container {
  width: 90%;
  margin: 0 auto;
  max-height: 60vh;
  overflow-y: auto;
}
.tipdialog_container::-webkit-scrollbar {
  width: 0;
}

.tipdialog_container .dialog1_content .van-checkbox {
  align-items: start !important;
}

::v-deep .van-dialog__header {
  font-size: 0.4rem;
  font-weight: 800;
}
</style>
