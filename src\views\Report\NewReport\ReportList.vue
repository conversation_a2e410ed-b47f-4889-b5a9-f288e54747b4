<template>
  <div>
    <div class="checksReports">
      <div class="clearfix title">
        <div class="fl">
          <van-dropdown-menu active-color="#ee0a24">
            <van-dropdown-item v-model="value" :options="option1" @change="change(value)" />
          </van-dropdown-menu>
        </div>
      </div>
      <div class="content" v-for="(item,index) in repData" :key="index">
        <div class="listBox">
          <div class="targetYear">{{item.years+' 年'}}</div>
          <ul id="resReport" class="list">
            <li
              class="item"
              v-for="(item2,index2) in item.listData"
              :key="index2"
              @click="gotourl(item2)"
            >
              <i class="line"></i>
              <div class="personalDetails">
                <span class="name">{{item2.report_Name}}</span>
              </div>
              <div class="message">{{item2.report_No+'（报告已出）'}}</div>
              <!-- <div class="message">**********（报告已出）</div> -->
              <div class="time">{{item2.report_Date}}</div>
              <div class="lookBtn">点击查看</div>
              <img src="../../../assets/report/itemBg.png" alt class="itemBg" />
            </li>
          </ul>
        </div>
      </div>
      <div v-if="repData.length==0" class="content">
        <div class="listBox">
          <ul class="list">
            <li class="item">
              <div class="personalDetails">
                <span class="name">暂无报告</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div v-if="!only" class="outbtn" @click="changeAccount()">退出登入</div>
    </div>
  </div>
</template>

<script>
import { storage, ajax, toolsUtils } from "../../../common";
import apiUrls from "../../../config/apiUrls";
import { Toast } from "vant"
export default {
  components: {},
  data() {
    return {
      value: 0,
      option1: [
        { text: '全部报告', value: 0 }
      ],
      title: "报告",
      baseData: [{ year: "", listData: [] }],
      dateChoose: "",
      user: {},
      dateList: [],
      repData: [],
      only: false,
    };
  },
  created() {
    try {
      this.user = JSON.parse(storage.session.get("reportList"));
      this.only = ((this.$route.query.type || '') == 'only');
      //   if(this.only==true)
      //   {
      //      this.$store.commit('setTeamData', {homeUrl:'/usercenter?type=only',homeTitle:'我的'});
      //   }
    } catch (error) {
      this.$router.push({ path: "/oauth?type=jump" });
    }
    this.GetReportList();
  },
  mounted() { },
  methods: {
    // chooseDatefn() {
    //   this.repData = [];
    //   this.baseData.forEach(element => {
    //     if (this.dateChoose == element.year || this.dateChoose == "0") {
    //       this.repData.push(element);
    //     }
    //   });
    // },

    GetReportList() {
      // this.dateChoose = "0";
      var pData = {
        idCard: this.user.idcard, //'T0000250',//this.user.pat_code_ny,
        name: this.user.name,
        tel: this.user.tel
      };
      ajax.post(apiUrls.GetReportList, pData, { nocrypt: true }).then(r => {
        var data = r.data.returnData;
        if (r.data.success) {
          this.baseData = data;
          this.repData = this.baseData;;
          for (var i = 0; i < this.repData.length; i++) {
            this.option1.push({
              text: this.repData[i].years,
              value: this.repData[i].years
            });
          }
        }
        else {
          alert("暂无报告数据");
        }
      })
        .catch(err => {
          Toast("网络异常！请稍后再试");
          console.log(err);
        });
    },
    gotourl(item) {
      // var encryptUrl=encodeURIComponent(toolsUtils.encrypt(url,this.user.pat_code.trim()+'nysyt'));
      // this.$router.push({ path: "/report/reportMain/" + encryptUrl });
      storage.session.set("pat_info", JSON.stringify(item))
      this.$router.push({ path: "/ReportMian" });
    },
    changeAccount() {
      //清除报告缓存
      storage.session.delete("reportList");
      storage.session.delete("pat_info");
      storage.session.delete("report");

      storage.session.set("redirect", "/report");
      this.$router.replace({ path: "/ReportLogin" });
    },
    change(value) {
      this.repData = [];
      this.baseData.forEach(i => {
        if (value == i.years || value == 0) {
          this.repData.push(i);
        }
      });
      //console.log(value);
    }
  },
  computed: {
    newtitle: function () {
      return this.title;
    }
  }
};
</script>

<style lang="scss" scoped>
.checksReports .title {
  padding: 0 0.24rem;
  line-height: 0.88rem;
  color: #646a6f;
  font-size: 0.28rem;
  background: #fafafa;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
}
.checksReports .title .fl {
  position: relative;
}

.checksReports .title .fl .dateTitle {
  display: inline-block;
  margin-left: 0.55rem;
  font-size: 120%;
  border: none;
  background: #fafafa;
  -webkit-appearance: menulist;
}
.checksReports .title .fr {
  position: relative;
}
.checksReports .title .fr > div:nth-child(1) {
  padding-right: 0.34rem;
}
.checksReports .title .fr img {
  position: absolute;
  width: 0.24rem;
  height: 0.14rem;
  top: 50%;
  margin-top: -0.07rem;
  right: 0;
}
.checksReports .title {
  color: #302f35;
}

.checksReports ul li a {
  color: #9e9e9e;
}
.checksReports .undone {
  color: #ff001f;
}

.checksReports .content {
  /* position: absolute; */
  width: 100%;
  top: 0.88rem;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
}
.checksReports .content .listBox {
  margin-left: 0.4rem;
  border-left: 1px solid #dbdbdb;
  height: 100%;
  box-sizing: border-box;
  padding-top: 0.48rem;
}
.checksReports .content .listBox .targetYear {
  background: #43a4eb;
  border-radius: 100px;
  width: 2rem;
  height: 0.64rem;
  line-height: 0.64rem;
  color: #fff;
  font-size: 0.28rem;
  padding-left: 0.68rem;
  margin-left: -0.3rem;
  position: relative;
  box-sizing: border-box;
}
.checksReports .content .listBox .targetYear:before {
  content: "";
  position: absolute;
  left: 0.27rem;
  top: 0.2rem;
  width: 0;
  height: 0;
  border-top: 0.12rem solid transparent;
  border-left: 0.16rem solid #fff;
  border-bottom: 0.12rem solid transparent;
}
.checksReports .content .listBox .list {
  overflow: hidden;
}
.checksReports .content .listBox .item {
  margin-top: 0.4rem;
  width: 89%;
  margin-left: 0.2rem;
  position: relative;
  background-image: linear-gradient(90deg, #3f9cdc 2%, #6d91f1 97%);
  border-radius: 3px;
  color: #fff;
  padding: 0.16rem 0.24rem 0.14rem;
  box-sizing: border-box;
}
.checksReports .content .listBox .item:last-child {
  margin-bottom: 0.4rem;
}
.checksReports .content .listBox .item .line {
  position: absolute;
  width: 0.4rem;
  height: 1px;
  top: 50%;
  left: -0.4rem;
  margin-top: -0.05px;
  background-color: #e0e0e0;
}
.checksReports .content .listBox .item .personalDetails {
  line-height: 0.5rem;
  margin-bottom: 0.08rem;
}
.checksReports .content .listBox .item .personalDetails span {
  vertical-align: top;
}
.checksReports .content .listBox .item .personalDetails .name {
  font-size: 0.36rem;
  margin-right: 0.32rem;
}
.checksReports .content .listBox .item .personalDetails .age {
  font-size: 0.28rem;
}
.checksReports .content .listBox .item .message,
.checksReports .content .listBox .item .time {
  font-size: 0.24rem;
  line-height: 0.34rem;
}
.checksReports .content .listBox .item .message {
  margin-bottom: 0.04rem;
}
.checksReports .content .listBox .item .lookBtn {
  border-radius: 50%;
  width: 0.96rem;
  height: 0.96rem;
  color: #797979;
  font-size: 0.24rem;
  background: rgba(255, 255, 255, 0.8);
  text-align: center;
  position: absolute;
  right: 0.24rem;
  top: 0.32rem;
  padding: 0.2rem;
  box-sizing: border-box;
}
.checksReports .content .listBox .item .itemBg {
  position: absolute;
  top: 0;
  right: 0;
  width: 1.66rem;
}

.outbtn {
  text-align: center;
  height: 0.6rem;
  background: #43a4eb;
  border-radius: 100px;
  color: #fff;
  font-size: 0.3rem;
  line-height: 0.6rem;
  margin: 0 15%;
}
</style>
<style >
.weui-dialog__bd:first-child {
  padding: 1em 20px 1em !important;
}
</style>