<template>
  <div>
    <div id="PersonalOrder">
      <!-- top -->
      <div class="Information">
        <div class="InformationBox">
          <div class="InformationDiv">
            <div class="InformationTitle">
              <b>体检人信息</b>
            </div>
            <div class="InformationInput"></div>
          </div>

          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>证件号</span>
            </div>
            <div class="InformationInput">
              <input
                type="text"
                placeholder="请输入身份证号"
                v-model="InputCards"
                maxlength="18"
              />
            </div>
          </div>

          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>姓名</span>
            </div>
            <div class="InformationInput">
              <input
                type="text"
                placeholder="请输入与身份证一致的姓名"
                v-model="InputNames"
                maxlength="6"
              />
            </div>
          </div>

          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>手机号</span>
            </div>
            <div class="InformationInput">
              <input
                type="text"
                placeholder="请输入手机号"
                v-model="InputTels"
                maxlength="11"
              />
            </div>
          </div>

          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>证件类型</span>
            </div>
            <div class="InformationDropDown" active-color="#ee0a24">
              <van-dropdown-menu>
                <van-dropdown-item
                  v-model="cardType"
                  :options="CardTypeList"
                  :closed="ShowOtherDate()"
                />
              </van-dropdown-menu>
            </div>
          </div>
        </div>
      </div>
      <!-- middle -->
      <div class="time">
        <div class="timeDiv">
          <div class="timeTitle">
            <span>体检日期</span>
          </div>
          <div class="timeChoice">
            <span>{{ date }}（{{ week }}）</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle">
            <span>报到时间</span>
          </div>
          <div class="timeChoice">
            <span>{{ calendarDay.sumtimeName }}</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle">
            <span>体检类型</span>
          </div>
          <div class="timeChoice">
            <span v-if="type == 'staff'">专科体检</span>
            <span v-else-if="type == 'person'">普通健康体检</span>
            <span v-else-if="type == 'vehicle'">贵宾体检</span>
            <span v-else>智能体检</span>
          </div>
        </div>
      </div>
      <div class="cluscs">
        <div class="timeDiv">
          <div class="timeTitle">
            <span v-if="!isAdd">套餐价格</span>
            <span v-else>总价格</span>
          </div>
          <div class="timeChoice">
            <span class="priceChoice">￥</span>
            <span>{{ chooseItem.totalPrice }}</span>
          </div>
        </div>
      </div>

      <!-- 套餐 -->
      <div class="SetMealNowA" v-if="clusIn.length > 0">
        <van-collapse v-model="clusShowA">
          <van-collapse-item name="2">
            <template #title>
              <div class="SetTitleA">
                <img src="../../assets/detailsSex01.png" alt />
                <div>
                  {{ dataList.clus_Name }}（共{{ clusIn.length }}项）<span
                    class="SetTitle-priceA"
                    >￥{{ dataList.price }}</span
                  >
                </div>
              </div>
            </template>
            <div class="set-contentA">
              <div
                class="content-itemA"
                v-for="(item, index) in clusIn"
                :key="index"
              >
                <span>{{ index + 1 }}、{{ item.comb_Name }}</span>
                <!-- <span class="item-priceA">￥{{ item.price }}</span> -->
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>

      <!-- 自选加项 -->
      <div class="SetMealNowA" v-if="clusOut.length > 0">
        <van-collapse v-model="clusShowB">
          <van-collapse-item name="1">
            <template #title>
              <div class="SetTitleA">
                <img src="../../assets/detailsSex01.png" alt />
                <div>
                  自选项目（共{{ clusOut.length }}项）
                  <!-- <span class="SetTitle-priceA">￥{{ price_out }}</span> -->
                </div>
              </div>
            </template>
            <div class="set-contentA">
              <div
                class="content-itemA"
                v-for="(item, index) in clusOut"
                :key="index"
              >
                <span>{{ index + 1 }}、{{ item.comb_Name }}</span>
                <!-- <span class="item-priceA">￥{{ item.price }}</span> -->
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>

      <div style="height: 1.32rem"></div>

      <!-- <div class="SetMealNow">
        <div class="NowBox">
          <div class="SetTitle">
            <div class="SetImg" v-if="dataList.sex == '1'">
              <img src="../../assets/detailsMan01.png" alt />
            </div>
            <div class="SetImg" v-else-if="dataList.sex == '2'">
              <img src="../../assets/detailsWoman01.png" alt />
            </div>
            <div class="SetImg" v-else-if="dataList.sex == '3'">
              <img src="../../assets/detailsSex01.png" alt />
            </div>
            <div class="SetText">
              <span>{{ dataList.clus_Name }}</span>
            </div>
          </div>
        </div>
      </div> -->

      <!-- foot -->
      <div class="confirm" @click="ToSuccess">
        <span>立即预约</span>
      </div>

      <!--遮罩层-->
      <van-overlay :show="show" v-show="show">
        <div class="vanoverBtn">
          <van-loading type="spinner" color="#1989fa">预约中...</van-loading>
        </div>
      </van-overlay>
    </div>
  </div>
</template>
<script>
import { ajax, dataUtils, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Toast } from "vant";
import Vue from "vue";

export default {
  data() {
    return {
      calendarDay: [],
      dataList: [],
      price: 0,
      CardText: "",
      cardType: "01",
      img: "",
      PersonalSpan: "",
      InputNames: "",
      InputCards: "",
      InputTels: "",
      type: "",
      specialItemName: "",
      show: false,
      showDateSexFlag: false,
      //证件类型集合
      CardTypeList: [
        { text: "请选择", value: "0" },
        { text: "居民身份证", value: "01" },
        { text: "居民户口薄", value: "02" },
        { text: "护照", value: "03" },
        { text: "军官证", value: "04" },
        { text: "驾驶证", value: "05" },
        { text: "港澳居民来往内地通行证", value: "06" },
        { text: "台湾居民来往内地通行证", value: "07" },
      ],
      chooseItem: {
        total: 0.0,
        chooseCombCode: "",
      }, //加减项选择的项目和总价对象
      //判断此版本是否需要加项功能
      IsAddClusItem: false,
      isAdd: false, //是否自选加项
      clusOut: [], //自选组合
      price_out: 0,
      clusShowB: [],
      clusIn: [],
      clusShowA: [],
      add_comb_code: "", //同步材料费数据
    };
  },
  created() {
    if (storage.session.get("onlyClus")) {
      this.add_comb_code = storage.session.get("onlyClus");
    }
    var user = JSON.parse(storage.cookie.get("user"));
    this.type = storage.session.get("type");
    this.specialItemName = storage.session.get("clusIncludeSpecialItem");
    this.InputNames = user.name;
    this.InputCards = user.idCard;
    this.InputTels = user.tel;
    this.dataList = JSON.parse(storage.session.get("dataList"));
    this.calendarDay = JSON.parse(storage.session.get("calendarDay"));
    this.chooseItem = JSON.parse(storage.session.get("chooseItem")); //获取加减项选择的项目和总价对象
    if (this.chooseItem && this.chooseItem.chooseCombCode) {
      // console.log("this.chooseItem",this.chooseItem);
      this.clusOut = this.chooseItem.chooseCombCode;
      this.price_out = dataUtils.priceSum(
        this.chooseItem.totalPrice,
        this.dataList.price,
        "T"
      );
      this.IsAddClusItem = true;
      if (this.chooseItem.add_comb_code) {
        this.add_comb_code = this.chooseItem.add_comb_code;
      }
    }

    this.date = this.calendarDay.date;
    var weekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    this.week = weekDay[this.calendarDay.week];
    if (this.dataList.clus_Name == "自选项目") {
      this.isAdd = true;
      this.price_out = this.chooseItem.totalPrice;
    } else {
      this.clusIn = JSON.parse(storage.session.get("clusIncludeItem"));
    }
  },
  methods: {
    //证件类型改变事件
    ShowOtherDate() {
      if (this.cardType === "01" || this.cardType === "0") {
        this.showDateSexFlag = false;
      } else {
        this.showDateSexFlag = true;
      }
    },
    ToSuccess() {
      if (this.InputNames == "") {
        Toast("请输入姓名");
        return;
      }
      if (this.InputCards == "") {
        Toast("请输入身份证件号码");
        return;
      }
      if (this.InputTels == "") {
        Toast("请输入手机号码");
        return;
      }
      if (dataUtils.isTel(this.InputTels) != true) {
        Toast(dataUtils.isTel(this.InputTels));
        return;
      }
      if (dataUtils.isCardID(this.InputCards) != true) {
        Toast(dataUtils.isCardID(this.InputCards));
        return;
      }
      if (this.dataList.sex != 3) {
        var Sex = parseInt(this.InputCards.substr(16, 1)) % 2;
        if (Sex == 1) {
          if (this.dataList.sex != 1) {
            Toast("请选择对应性别的套餐！");
            return;
          }
        }
        if (Sex == 0) {
          if (this.dataList.sex != 2) {
            Toast("请选择对应性别的套餐！");
            return;
          }
        }
      }

      //问卷处理
      let quest = storage.session.get("question");
      if (quest) {
        let questions = JSON.parse(quest);
        questions.z9 = this.InputCards;
        //按键名排序
        let sortedKeys = Object.keys(questions).sort();
        let sortedObject = {};
        sortedKeys.forEach((key) => {
          sortedObject[key] = questions[key];
        });
        quest = JSON.stringify(sortedObject);
      }

      if (this.IsAddClusItem) {
        var pData = {
          openid: storage.cookie.get("openid"),
          name: this.InputNames,
          idCard: this.InputCards,
          tel: this.InputTels,
          clus_Name: this.dataList.clus_Name,
          clus_Code: this.dataList.clus_Code,
          begin_Time: this.calendarDay.date,
          type: this.type,
          sumtime_Code: this.calendarDay.sumtime_Code,
          sumtime_Name: this.calendarDay.sumtimeName,
          // price:this.dataList.price,
          price: this.chooseItem.totalPrice,
          choose_comb_code: JSON.stringify(this.chooseItem.chooseCombCode),
          // remark: this.specialItemName,
          zx_price: this.price_out, //自选总价
          clus_price: this.dataList.price, //套餐价格
          kw: this.chooseItem.chooseCombCode.map((x) => x.comb_Code).join(","), //查询部位数
          remark: quest, //个检问卷
          add_comb_code: this.add_comb_code, //加项计算材料费的同步数据
        };
      } else {
        var pData = {
          openid: storage.cookie.get("openid"),
          name: this.InputNames,
          idCard: this.InputCards,
          tel: this.InputTels,
          clus_Name: this.dataList.clus_Name,
          clus_Code: this.dataList.clus_Code,
          begin_Time: this.calendarDay.date,
          type: this.type,
          sumtime_Code: this.calendarDay.sumtime_Code,
          sumtime_Name: this.calendarDay.sumtimeName,
          price: this.dataList.price,
          // remark: this.specialItemName,
          remark: quest, //个检问卷
          add_comb_code: this.add_comb_code, //加项计算材料费的同步数据
        };
      }
      // console.log("pData",pData);return
      this.show = true;
      ajax
        .post(apiUrls.PersonOrderAdd, pData, { nocrypt: true })
        .then((r) => {
          if (r.data.success) {
            storage.session.set(
              "OrderList",
              JSON.stringify(r.data.returnData.ord)
            );
            //更新cookie信息
            storage.cookie.set("user", JSON.stringify(r.data.returnData.uid));
            //Toast(r.data.returnMsg);
            setTimeout(() => {
              Toast("预约成功，请携带身份证到现场体检");
              this.$router.replace({
                path: "/PersonPayment",
              });
              this.show = false;
            }, 2000);
          } else {
            this.show = false;
            Toast(r.data.returnMsg);
            return;
          }
        })
        .catch((e) => {
          alert("系统繁忙！请稍后再试");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
#PersonalOrder .Information {
  width: 100%;
  height: 4.48rem;
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.Information .InformationBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.InformationBox .InformationDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.InformationDiv .InformationTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.InformationDiv .InformationDropDown {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  margin-right: 0.2rem;
  justify-content: flex-end;
  align-items: center;

  .van-dropdown-menu {
    height: 45px !important;
  }
}

.cluscs {
  width: 100%;
  height: 0.87rem;
  background: white;
  margin-top: 0.16rem;
}

.cluscs .timeDiv {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
}

.cluscs .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.cluscs .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.InformationDiv .InformationInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  margin-right: 0.1rem;
}

.InformationInput input {
  width: 100%;
  /* // height: 100%; */
  border: none;
  outline: medium;
  text-align: right;
}

#PersonalOrder .time {
  width: 100%;
  height: 2.6rem;
  background: white;
  margin-top: 0.16rem;
}

.time .timeDiv {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
}

.timeDiv .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.timeDiv .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.timeChoice .priceChoice {
  font-size: 0.24rem;
  color: #d0021b;
  letter-spacing: -0.02px;
}

.timeChoice span:nth-child(2) {
  font-size: 0.4rem;
  color: #d0021b;
  letter-spacing: -0.02px;
  line-height: 0.36rem;
}
//加项
#PersonalOrder .SetMealNowA {
  width: 100%;
  /* height: 2.66rem; */
  background: white;
  margin-top: 0.16rem;
  // padding: 0.36rem 0;
}
.SetMealNowA .NowBoxA {
  width: 92%;
  height: 50%;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  overflow: hidden;
}
.SetTitleA {
  // width: 100%;
  // height: 1rem;
  display: flex;
  align-items: center;
}
.SetTitleA img {
  margin-right: 0.1rem;
}
.SetTitle-priceA {
  color: #d0021b;
}

.set-content {
  font-size: 0.3rem;
  color: #b7b7b7;
}
.content-itemA {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.12rem;
}
.item-priceA {
  color: #d0021b;
}

/* 套餐 */
#PersonalOrder .SetMealNow {
  width: 100%;
  /* height: 2.66rem; */
  background: white;
  margin-top: 0.16rem;
  padding: 0.36rem 0;
}

.SetMealNow .NowBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.NowBox .SetTitle {
  width: 100%;
  height: 1rem;
  display: flex;
}

.SetTitle .SetImg {
  width: 0.75rem;
  height: 1rem;
  display: flex;
  /* // justify-content: start; */
  align-items: center;
}

.SetImg img {
  margin-top: 3px;
}

.SetTitle .SetText {
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.NowBox .MealText {
  width: 100%;
  min-height: 1.3rem;
  max-height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  overflow-y: scroll;
}

/* 跳转按钮 */
.confirm {
  width: 92%;
  height: 0.96rem;
  background: #6a9be4;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancelfirm {
  width: 92%;
  height: 0.96rem;
  background: #b7b7b7;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 消息提示 */
.text-tip {
  display: block;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 15px 15px;
  line-height: 18px;
  position: fixed;
  left: 50%;
  bottom: 55%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
  border-radius: 3px;
  display: none;
  z-index: 9999;
  font-size: 14px;
  text-align: center;
}

.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.vanoverBtn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}
</style>
