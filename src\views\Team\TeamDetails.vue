<template>
  <div>
    <div id="details">
      <!-- 入职套餐 -->
      <div class="detailsDiv">
        <div class="detailsTop">
          <div class="TopImg">
            <img src="../../assets/detailsSex.png" alt />
          </div>
          <div class="TopTitle">
            <span>{{clusName}}</span>
          </div>
          <div class="TopBtn">
            <div>
              <span>基础套餐</span>
            </div>
          </div>
        </div>
      </div>

      <!-- tab -->
      <div class="detailsDiv TabDiv">
        <div class="detailsTab">
          <div class="TabBtn">
            <div :class="BtnState==true?'BtnBlueL':'BtnWhiteL'" @click="btnIntroduction">套餐简介</div>
            <div :class="btnKnowcs==true?'BtnBlueR':'BtnWhiteR'" @click="btnKnow">体检须知</div>
          </div>
          <div class="TabText">
            <div v-if="TabText" class="TabSpan">
              <span>{{PersonalSpan}}</span>
            </div>
            <div v-else>
              <div class="NoticeDiv" v-for="(item,index) in NoticeDiv" :key="index">
                <div class="NoticeNumber">
                  <span>{{index+1}}.</span>
                </div>
                <div class="NoticeText">
                  <span>{{item.NoticeText}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="bottomDiv"></div>
        </div>
      </div>

      <!-- 套餐项目 -->
      <div class="SetMeal">
        <div class="detailsDiv SetMealBottom">
          <div class="SetMealA">
            <div class="SetMealImg">
              <img src="../../assets/SetMeal.png" alt="套餐项目" />
            </div>
            <span>套餐项目&nbsp;&nbsp;({{detailed.length}}项)</span>
          </div>
        </div>
        <div class="detailsDiv">
          <div class="detailed" v-for="(items,indexS) in detailed" :key="indexS">
            <div class="detailedTitle">
              <span>{{items.comb_Name}}</span>
            </div>
            <div class="detailedText">
              <span>{{items.note}}</span>
            </div>
            <div class="detailedBottom"></div>
          </div>
        </div>
      </div>

      <div class="footFixed">
        <div class="goto" @click="GoBack">
          <span>返回预约</span>
        </div>
      </div>
      <div class="footDiv"></div>
    </div>
  </div>
</template>
<script>
import { ajax, storage } from '../../common'
import apiUrls from '../../config/apiUrls'
export default {
  data() {
    return {
      btnKnowcs: true,
      PersonalSpan: '入职通用体检项目，适用于普通入职体检人群，如需增加项目，请于体检当日在前台办理签到时向工作人员寻求帮助',
      // Tab按钮样式
      BtnState: false,
      // Tab页文本显示
      TabText: false,
      // 体检须知文本
      NoticeDiv: [{
        NoticeText: '提前预约，为了成功提交订单，您最晚要在体检前1天(具体以网站公示的号源情况为准)预订，请尽早预订。'
      }, {
        NoticeText: '如单位对体检项目有特殊要求，请于体检日在我中心前台进行具体咨询与调整。'
      }, {
        NoticeText: '以上所有解释权归本健康体检中心所有！'
      }],
      // 项目套餐
      detailed: [],
      // 套餐长度
      SetMealLength: 11,
      // 套餐名
      clusName: "",
      reg_no:""
    }
  },
  created() {
    this.SetMealLength = this.detailed.length;
    var info = JSON.parse(storage.session.get("teamInfo"));
    this.clusName = info[0].clus_name;
    this.reg_no = info[0].reg_no;
    this.GetClusItemDetail(info[0].clus_code);
  },
  methods: {
    // 套餐简介
    btnIntroduction: function () {
      this.BtnState = true;
      this.btnKnowcs = false;
      this.TabText = true;
    },
    // 体检须知
    btnKnow: function () {
      this.btnKnowcs = true;
      this.BtnState = false;
      this.TabText = false;
    },
    // 返回上一页
    GoBack() {
      window.history.go(-1);
    },
    //获取项目套餐
    GetClusItemDetail(comb_code) {
      var that = this;
      var pData = {
        comb_code: comb_code,
        regno:this.reg_no
      }
      ajax.post(apiUrls.GetItemCombList, pData, { nocrypt: true }).then(r => {
        if (r.data.success) {
          var data = r.data.returnData;
          that.detailed = data;
          let special = '';
          for (let i = 0; i < data.length; i++) {
            const elem = data[i];
            if (elem.comb_Code.trim() === '3034' || elem.comb_Code.trim() === '3035' || elem.comb_Code.trim() === '3003' || elem.comb_Code.trim() === '3025') {
              special += elem.comb_Name;
            }              
          }
          storage.session.set("clusIncludeSpecialItem", special);
        } else {
          alert(r.data.returnMsg);
        }
      }).catch(e => {
        alert("系统繁忙！请稍后再试");
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.detailsDiv {
  width: 100%;
  background: white;
  box-sizing: border-box;
}

.detailsDiv .detailsTop {
  width: 92%;
  height: 1.24rem;
  margin: 0 auto;
  font-size: 0.28rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopImg {
  width: 1rem;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopTitle {
  width: calc(100% - 2.16rem);
  height: 100%;
  font-size: 0.36rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.TopTitle span {
  margin-left: 0.15rem;
}

.detailsTop .TopBtn {
  width: 1.4rem;
  height: 100%;
  color: #ffffff;
  letter-spacing: -0.04rem;
  display: flex;
  align-items: center;
}

.TopBtn div {
  width: 1.4rem;
  height: 0.48rem;
  background: #6a9be4;
  border-radius: 0.04rem;
  text-align: center;
  line-height: 0.48rem;
}

/* Tab */
.TabDiv {
  border-top: 0.04rem solid #dfe3e9;
}

.detailsDiv .detailsTab {
  width: 92%;
  margin: 0 auto;
}

.detailsTab .TabBtn {
  width: 100%;
  height: 0.64rem;
  font-size: 0.28rem;
  display: flex;
  margin-top: 0.24rem;
}

.TabBtn .BtnBlueL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnWhiteL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnBlueR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 0 2px 2px 0;
}

.TabBtn .BtnWhiteR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 0 2px 2px 0;
}

.detailsTab .TabText {
  width: 100%;
  min-height: 1rem;
  font-size: 0.28rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin-top: 0.2rem;
}

.detailsTab .bottomDiv {
  width: 100%;
  height: 0.3rem;
}

.TabSpan {
  margin-top: 0.08rem;
}

.TabText .NoticeDiv {
  width: 100%;
  margin-top: 0.08rem;
  display: flex;
}

.NoticeDiv .NoticeNumber {
  width: 0.28rem;
}

.NoticeDiv .NoticeText {
  width: calc(100% - 0.28rem);
}

#details .SetMeal {
  width: 100%;
  font-size: 0.28rem;
  color: #4a4a4a;
  box-sizing: border-box;
  letter-spacing: -0.01px;
  margin-top: 0.16rem;
}

.SetMeal .SetMealBottom {
  border-bottom: 1px solid #dfe3e9;
}

.SetMeal .SetMealA {
  width: 92%;
  height: 0.84rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
}

.SetMealA .SetMealImg {
  width: 0.4rem;
  height: 0.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.SetMealImg img {
  margin-top: 3px;
}

.SetMealA span {
  margin-left: 0.1rem;
}

.SetMeal .detailed {
  width: 92%;
  margin: 0 auto;
  border-bottom: 1px solid #dfe3e9;
}

.detailed .detailedTitle {
  width: 100%;
  height: 0.8rem;
  display: flex;
  align-items: center;
  font-weight: bold;
}

.detailed .detailedText {
  color: #535151;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
}

.detailed .detailedBottom {
  width: 100%;
  height: 0.24rem;
}

#details .footFixed {
  width: 100%;
  height: 1.36rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
}

.footFixed .goto {
  width: 92%;
  height: 0.96rem;
  background: #6a9be4;
  border-radius: 5px;
  margin: 0.2rem auto;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

#details .footDiv {
  width: 100%;
  height: 1.16rem;
}
</style>