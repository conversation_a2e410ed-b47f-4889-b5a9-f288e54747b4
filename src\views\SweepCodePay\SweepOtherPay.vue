<template>
  <div>
    <div id="wrap">
      <div class="success-page" v-if="showT">
        <!-- head -->
        <div class="success-head">
          <!-- <img src="~/Content/img/chongzhichenggong.png" alt=""> -->
          <img src="../../assets/PaySuccess.png" alt />
          <h3>确定信息是否有误</h3>
        </div>
        <!-- content -->
        <div class="success-content">
          <div class="list-wrap">
            <p>体检用户:</p>
            <span>{{ PayRecord.name }}</span>
          </div>
          <div class="list-wrap">
            <p>体 检 号:</p>
            <span>{{ regno }}</span>
          </div>
          <div class="list-wrap">
            <p>商品编号:</p>
            <span>{{ PayRecord.goods_code }}</span>
          </div>

          <div class="list-wrap">
            <p>支付金额:</p>
            <span style="color: red;">￥{{ PayRecord.pay_amount }}</span>
          </div>

          <div class="list-wrapa">
            <div>商品集合({{ combArry.length }} 项)：</div>
            <!-- <img v-show="!showTable" src="../../assets/report/down.png" alt="" class="down"/>
						<img v-show="showTable" src="../../assets/report/up.png" alt="" class="down"/> -->
          </div>
          <div class="costTitle" v-if="combArry.length">
            <div v-for="(item, index) in combArry" :key="index" class="itemcos">
              {{ item.goods_name }}
            </div>
          </div>
        </div>

        <!-- button -->
        <div class="footer-btn" v-if="comfimPay == true">
          <button @click="lookOrder()">取消支付</button>
          <button @click="PayAmoutOnlick()" :style="{ 'background-color': payDisabled ? '#9c918d' : '' }">确定支付</button>
        </div>
        <div class="past" v-else>
          <span>订单已过期</span>
        </div>
      </div>

      <!-- 暂无缴费信息 -->
      <div v-if="!showT">
        <div class="clusFlag">
          <div><img src="../../assets/kong.png" /></div>
          <span style="margin-top: 10px;color: dimgray;">暂无缴费信息或已完成缴费</span>
        </div>
        <div style="margin-top: 0.3rem;">
          <van-button type="primary" size="large" color="linear-gradient(to right, #4bb0ff, #888bf2)"
            @click="$router.push('/')">
            返回
          </van-button>
        </div>
      </div>

      <!-- 遮罩层\加载 -->
      <van-overlay :show="showOvly" v-show="showOvly">
        <!-- <div class="vanoverBtn"> -->
        <van-loading type="spinner" color="#fff" vertical size="1rem" text-size="0.3rem">加载中...</van-loading>
        <!-- </div> -->
      </van-overlay>



    </div>
  </div>
</template>
<script>
import { storage, ajax } from "../../common";
import { Toast } from "vant";
import apiUrls from "../../config/apiUrls";
import wx from "weixin-jsapi";
import Vue from "vue"

export default {
  data() {
    return {
      comfimPay: true,
      isShowNote: true,
      time: "",
      PayRecord: {},
      payInfo: {},
      regno: "",
      chrg_no: "",
      price: 0,
      payDisabled: true,//是否禁止点击支付按钮
      // showTable:false,
      combArry: [],//项目组合
      showOvly: true,
      showT: true,
      openid: "",
    };
  },
  created() {
    this.regno = this.$route.query.reg_no;
    this.chrg_no = this.$route.query.chrg_no;
    this.price = this.$route.query.paymoney;
    this.getList(); //通过体检号查询信息渲染页面
  },
  methods: {
    // http://localhost:8080/#/SweepOtherPay?reg_no=PT25000610&chrg_no=387743&paymoney=73.46
    getList() {
      if (!this.regno) {
        Toast("获取体检号失败！");
        return;
      }
      if (!this.chrg_no) {
        Toast("获取chrg_no失败！");
        return;
      }
      if (!this.price) {
        Toast("获取price失败！");
        return;
      }
      let pData = {
        regno: this.regno,
        out_trade_no: this.chrg_no,
        price: this.price
      };
      ajax
        .post(apiUrls.GetOtherPayInfo, pData, { nocrypt: true })
        .then(r => {
          if (r.data.success == false) {
            Toast(r.data.returnMsg);
            this.showOvly = false;
            this.showT = false;
            return;
          }

          let result = JSON.parse(r.data.returnData);
          this.payInfo = result;

          this.PayRecord = JSON.parse(result.data);
          // console.log(this.payInfo, this.PayRecord);
          this.combArry = this.PayRecord.goods_list;
          setTimeout(() => {
            this.payDisabled = false;
            this.showOvly = false;
          }, 3000);
          //获取openid
          this.openid = storage.cookie.get("openid");
        })
        .catch(e => {
          Toast("系统异常！请联系工作人员");
          return;
        });
    },

    // openid:storage.cookie.get("openid"), 获取openid
    PayAmoutOnlick() {

      //禁止点击支付按钮
      if (this.payDisabled) {
        return;
      }

      if (!this.openid) {
        storage.cookie.delete("openid");
        storage.cookie.delete("user");
        this.$router.push("/oauth");
        return
      }
      this.payDisabled = true;
      this.showOvly = true;

      let pdata = {
        kw: JSON.stringify(this.PayRecord),//inBodyJson
        regno: this.regno,
        out_trade_no: this.$route.query.chrg_no,
        price: this.$route.query.paymoney,
        code: this.payInfo.code,
        openid: this.openid
      }

      ajax
        .post(apiUrls.CreateOtherPayOrders, pdata, { nocrypt: true })
        .then(r => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          };
          var that_ = this;
          let dataA = JSON.parse(r.data.returnData).paymsg;
          let payData=JSON.parse(JSON.parse(dataA).credential.tranPackage)

          WeixinJSBridge.invoke(
            "getBrandWCPayRequest",
            {
              appId: payData.appId, //公众号名称，由商户传入
              timeStamp: payData.timeStamp, //时间戳
              nonceStr: payData.nonceStr, //随机串
              package: payData.package, //扩展包
              signType: payData.signType, //微信签名方式
              paySign: payData.paySign //微信签名
            },
            function (res) {
              switch (res.err_msg) {
                case "get_brand_wcpay_request:cancel":
                  alert("取消支付");
                  that_.$router.replace("/");
                  break;
                case "get_brand_wcpay_request:fail":
                  alert(
                    "支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。"
                  );
                  break;
                case "get_brand_wcpay_request:ok":
                  alert("支付成功");
                  that_.$router.replace("/");
                  break;
              }
            }
          );
          //设置支付按钮可以点击
          this.payDisabled = false;
        })
        .catch(e => {
          //设置支付按钮可以点击53366757
          this.payDisabled = false;
          alert("系统繁忙！请稍后再试");
          return;
        });
    },
    lookOrder() {
      this.$router.replace("/");
    }
  }
};
</script>
<style lang="scss" scoped>
.past {
  width: 80%;
  background-color: #939aa2;
  text-align: center;
  margin: 0 auto;
  display: flex;
  height: 1rem;
  justify-content: center;
  align-items: center;
  border-radius: 0.2rem;
  margin-top: 0.3rem;
}

.past span {
  font-size: 22px;
  color: #fff;
}

.success-page {
  padding: 0.2rem 0;
  background: #f0eff6;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.success-head {
  padding: 0.36rem 0 0.44rem 0;
}

.success-head img {
  width: 1.52rem;
  height: 1.52rem;
  display: block;
  margin: 0 auto 0.2rem;
}

.success-head h3 {
  text-align: center;
  font-size: 0.4rem;
  color: #354052;
  font-weight: normal;
}

.success-content {
  width: 96%;
  background: #fff;
  border-radius: 5px;
  padding: 0.4rem 0;
  margin-bottom: 0.48rem;
  margin: 0 auto;
}

.success-content .list-wrap {
  display: flex;
  height: 0.44rem;
  align-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  overflow: hidden;
  margin-bottom: 0.24rem;
  padding-left: 0.28rem;
}

.success-content .list-wrapa {
  display: flex;
  // height: 0.44rem;
  flex-direction: row;
  align-items: center;
  // justify-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  overflow: hidden;
  margin-bottom: 0.24rem;
  padding-left: 0.28rem;
}

.costTitle {
  max-height: 3rem;
  min-height: 2rem;
  overflow: auto;
}

.costTitle .itemcos {
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 24rpx;
  align-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  justify-items: center;
}

// .list-title{
// 		 display: flex;
// 		 justify-content: space-between;
// 		 align-items: center;
// 	 }

.success-content .list-wrap:last-child {
  margin-bottom: 0;
}

.success-content .list-wrap span {
  color: #354052;
  flex-grow: 1;
  /*text-align: right;*/
}

.footer-btn {
  display: flex;
  justify-content: space-around;
  margin-top: 0.5rem;
}

.footer-btn button {
  width: 40%;
  height: 1.08rem;
  border-radius: 5px;
  background: #80b1eb;
  border: 1px solid #80b1eb;
  display: block;
  font-size: 0.36rem;
  color: #ffffff;
}

.footer-btn button a {
  color: #ffffff;
}

.tip {
  /* margin-bottom: 1rem; */
  margin-top: 0.2rem;
  color: red;
  font-size: 0.3rem;
  justify-content: center;
}
</style>