<template>
  <div class="infoClass">
    <!-- 医院展示语句 -->
    <div class="infoText" v-html="infoData"></div>
    <div class="affirmBtn" id="Cancel">
      <button class="btninfos" @click="$router.back()">返回</button>
      <!-- <a href="javascript:void(0)">返回</a> -->
      <!-- <van-button plain type="info">返回</van-button> -->
    </div>
  </div>
</template>
<script>
import apiUrls from "../../config/apiUrls";
import { ajax } from "../../common";
export default {
  data() {
    return {
      infoData: "", //医院介绍文档
    };
  },
  created() {
    //浏览器打开不能复制粘贴
    this.$nextTick(() => {
      // 禁用右键
      document.oncontextmenu = new Function("event.returnValue=false");
      // 禁用选择
      document.onselectstart = new Function("event.returnValue=false");
    });
    this.GetNoticeInfo();
  },
  methods: {
    GetNoticeInfo() {
      let pData = {
        titleName: "体检须知",
      };
      ajax
        .post(apiUrls.GetNoticeInfo, pData, { nocrypt: true })
        .then((r) => {
          this.infoData = r.data.returnData.content;
        })
        .catch(() => {
          this.$router.go(-1);
          this.$toast("加载失败，请联系工作人员！");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
@mixin toCenter($justify, $items) {
  display: flex;
  justify-content: $justify;
  align-items: $items;
}
.infoClass {
  width: 100%;
  min-height: 100vh;
  position: relative;
  top: 0;
  font-size: 0.32rem;
  color: #4a4a4a;
  background-color: antiquewhite !important;
  .infoText {
    margin: 0 auto;
    width: 90%;
    //white-space: pre-line;
    // line-height: 25px;
    /deep/ p {
      margin: 0;
    }
    /deep/ h3 {
      margin: 0;
    }
  }
  .affirmBtn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 2rem;
  }
  .btninfos {
    width: 80%;
    height: 50%;
    background-color: cornflowerblue;
    font-size: 18px;
    border-radius: 20px;
    border: none;
    color: azure;
    letter-spacing: 12px;
  }
}
</style>
