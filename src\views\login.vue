<template>
  <!--登录-->
  <div class="login">
    <div class="hspLogo">
      <img src alt />
    </div>
    <div
      style="
        text-align: center;
        font-size: 20px;
        margin-top: 10px;
        font-weight: 500;
      "
    >
      中国人民解放军南部战区总医院健康体检中心
    </div>
    <div class="login_content">
      <form>
        <select name="idcardtype" id="cardtype" @change="cardTab">
          <option value="1">身份证</option>
          <option value="2">其它证件</option>
        </select>
        <div class="clearfix user_name" v-if="isReg > 0">
          <div class="fl left">
            <img src alt />
          </div>
          <div class="fl right">
            <input
              type="text"
              ref="refname"
              id="refname"
              placeholder="与证件一致的真实姓名"
              v-model="accmodel.name"
            />
          </div>
        </div>

        <div class="clearfix id_card">
          <div class="fl left">
            <img src alt />
          </div>
          <div class="fl right">
            <input
              type="text"
              v-model="accmodel.idcard"
              :placeholder="cardIndex == 1 ? '身份证号' : '其它证件号'"
            />
          </div>
        </div>

        <div class="clearfix user_sex" v-if="isReg > 0 && cardIndex == 2">
          <div class="fl left">
            <img src alt />
          </div>
          <div class="fl right">
            <input
              type="text"
              placeholder="性别"
              v-model="accmodel.sex"
              @click="dialogVisible = true"
              readonly
            />
          </div>
        </div>

        <div class="clearfix user_birthday" v-if="isReg > 0 && cardIndex == 2">
          <div class="fl left">
            <img src alt />
          </div>
          <div class="fl right">
            <group>
              <datetime
                placeholder="出生日期"
                value-text-align="left"
                :min-year="1900"
                :max-year="new Date().getFullYear()"
                v-model="accmodel.birthday"
              ></datetime>
            </group>
          </div>
        </div>

        <div class="clearfix user_phone">
          <div class="fl left">
            <img src alt />
          </div>
          <div class="fl clearfix right">
            <input
              type="tel"
              v-model="accmodel.tel"
              :readOnly="canYzm"
              maxlength="11"
              onblur="this.v();"
              placeholder="手机号码"
              onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)"
              class="fl"
            />
            <input
              type="button"
              class="fl getCode"
              @click="getYzm()"
              :class="{ 'btn-disabled': canYzm }"
              v-model="yzmNum"
              :disabled="canYzm"
              :plain="true"
            />
          </div>
        </div>

        <div class="clearfix code">
          <div class="fl left">
            <img src alt />
          </div>
          <div class="fl right">
            <input type="text" v-model="accmodel.yzm" placeholder="验证码" />
          </div>
        </div>

        <!-- <div class="clearfix" style="font-size:.25rem">
                 <span>点击登录表示您已阅读并同意</span>
                 <span style="color:blue">《优康用户协议》</span>
        </div>-->

        <input
          type="button"
          class="formBtn"
          value="登录"
          @click="onLgnOrReg()"
          v-if="isReg == 0"
          :plain="true"
        />
        <input
          type="button"
          class="new-user"
          value="新用户注册"
          @click="newReg()"
          v-if="isReg == 0 && isTeam != true"
        />

        <input
          type="button"
          class="formBtn"
          value="确认注册"
          @click="onLgnOrReg()"
          v-if="isReg == 1"
          :plain="true"
        />
        <input
          type="button"
          class="new-user"
          value="登录"
          @click="newReg()"
          v-if="isReg == 1"
        />
      </form>
    </div>
    <div class="tip">
      <a href="tel:020-88653363">客服热线:020-88653363</a>
      <i>|</i>
      <a href="javascript:void(0)" @click="tipshow(true)">常见问题</a>
    </div>

    <div v-show="tipinfo">
      <div class="tipinfo">
        <div class="tipinfo_head">
          <div class="title">常见问题</div>
          <div class="cancel" @click="tipshow(false)">&times;</div>
        </div>
        <div class="tipinfo_content">
          <p>1 团检用户第一次需要绑定身份证和手机号码以及姓名！</p>
          <p>2 报告用户第一次需要绑定身份证和手机号码以及姓名！</p>
          <p>
            3
            用户遇到手机号码、姓名和身份证不匹配，请携带本人身份证到我院进行更改！
          </p>
          <p>
            4
            用户用户登入后，保持15天默认登录，需求切换用户，在内页进行登出再登入！
          </p>
        </div>
      </div>
      <div class="mask" style="display: block"></div>
    </div>

    <div v-show="regInfo">
      <div class="c-regInfo">
        <div class="reg-head">
          <div class="reg-img">
            <img src alt />
          </div>
          <p class="reg-tip">请仔细确认您的注册信息</p>
        </div>
        <div class="reg-content">
          <div class="reg-line">
            <div class="reg-label">姓名</div>
            <div class="reg-txt">{{ accmodel.name }}</div>
          </div>
          <div class="reg-line">
            <div class="reg-label">
              {{ cardIndex == 1 ? "身份证" : "证件号" }}
            </div>
            <div class="reg-txt">{{ accmodel.idcard }}</div>
          </div>
          <div class="reg-line" v-if="isReg > 0 && cardIndex == 2">
            <div class="reg-label">性别</div>
            <div class="reg-txt">{{ accmodel.sex }}</div>
          </div>
          <div class="reg-line" v-if="isReg > 0 && cardIndex == 2">
            <div class="reg-label">出生日期</div>
            <div class="reg-txt">{{ accmodel.birthday }}</div>
          </div>
          <div class="reg-line">
            <div class="reg-label">电话</div>
            <div class="reg-txt">{{ accmodel.tel }}</div>
          </div>
        </div>
        <div class="reg-button">
          <div class="reg-button-left" @click="backReg()">返回修改</div>
          <div class="reg-button-right" @click="surePost()" :plain="true">
            确认注册
          </div>
        </div>
        <div class="reg-remind">
          <p>温馨提示:姓名或身份信息不准确可能会导致您</p>
          <p>在现场无法顺利办理体检，请认真核对</p>
        </div>
      </div>
      <div class="mask2" style="display: block"></div>
    </div>

    <!--性别选择框-->
    <div class="diaLog">
      <el-dialog
        :visible.sync="dialogVisible"
        top="36vh"
        :show-close="false"
        center
      >
        <div
          style="
            width: 100%;
            height: 0.6rem;
            line-height: 0.6rem;
            font-weight: 600;
            text-align: center;
          "
        >
          (必填)点击选择性别
        </div>
        <div class="radioDiv">
          <div class="radioBox" @click="OndiaClcik('男')">
            <div class="greenDiv">
              <div :class="accmodel.sex === '男' ? 'Smear' : 'full'"></div>
            </div>
            <div class="textNow">男</div>
          </div>
          <div class="radioBox" @click="OndiaClcik('女')">
            <div class="greenDiv">
              <div :class="accmodel.sex === '女' ? 'Smear' : 'full'"></div>
            </div>
            <div class="textNow">女</div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      title: "登录",
      IsBack: false,
      isReg: 0,
      canYzm: false,
      timeWait: 60,
      baseTime: 60,
      yzmNum: "获取验证码",
      tipinfo: false,
      accmodel: {
        name: "",
        idcard: "",
        tel: "",
        yzm: "",
        sex: "",
        birthday: "",
      },
      postData: {}, //注册提交的数据
      regInfo: false, //提示注册信息
      isTeam: false, //判断是否是由团检授权而来
      cardIndex: 1, //1为身份证，2为其它证件
      dialogVisible: false, //是否弹出性别选择框
    };
  },
  created() {},
  methods: {},
};
</script>
<style>
.login {
  position: absolute;
  width: 100%;
  height: 100%;
  /* background: url(../assets/img/bottomBg.png) 0 bottom/100% no-repeat; */
  background-color: #fff;
  padding-top: 0.4rem;
  box-sizing: border-box;
}
.login .hspLogo {
  text-align: center;
}
.login .hspLogo img {
  width: 1.92rem;
  height: 1.92rem;
  /* display: inline; */
}
.login .login_content {
  width: 5.14rem;
  margin: 0.4rem auto 0.24rem;
}
.login .login_content form > div {
  margin-bottom: 0.1rem;
}
.login .login_content form > div:last-of-type {
  margin-bottom: 0;
}
.login .login_content img {
  display: inline;
  vertical-align: top;
}
.login .login_content .user_name img {
  width: 0.48rem;
}
.login .login_content .id_card img {
  width: 0.64rem;
}
.login .login_content .user_sex img {
  width: 0.64rem;
}
.login .login_content .user_birthday img {
  width: 0.64rem;
}
.login .login_content .user_phone img {
  width: 0.46rem;
}
.login .login_content .code img {
  width: 0.64rem;
}
.login .login_content .left {
  width: 14.98054475%;
  text-align: center;
  margin-right: 6.22568093%;
  padding-top: 0.22rem;
}
.login .login_content .right {
  width: 78.79377432%;
  border-bottom: 1px solid #e6e6e6;
  user-select: auto;
  -webkit-user-select: auto;
}
.login .login_content .right input {
  width: 100%;
  height: 0.86rem;
  color: #4a4a4a;
  font-size: 0.32rem;
  border: none;
  outline: none;
  user-select: auto;
  -webkit-user-select: auto;
}

.login .login_content .user_phone input {
  width: 60.49382716%;
  user-select: auto;
  -webkit-user-select: auto;
}

.login .login_content .user_phone .getCode {
  background-image: linear-gradient(-179deg, #07a8d9 0%, #018bf0 100%);
  border-radius: 0.16rem;
  height: 0.64rem;
  line-height: 0.64rem;
  text-align: center;
  color: #fff;
  font-size: 0.24rem;
  width: 39.50617284%;
  margin-top: 0.12rem;
}
.login .login_content form .formBtn {
  width: 100%;
  border: none;
  outline: none;
  background-image: linear-gradient(-179deg, #07a8d9 0%, #018bf0 100%);
  border-radius: 0.08rem;
  color: #fff;
  font-size: 0.32rem;
  text-align: center;
  height: 0.88rem;
  line-height: 0.88rem;
  margin-top: 0.4rem;
}

.login .login_content form .new-user {
  width: 100%;
  border: none;
  outline: none;
  background-image: linear-gradient(-179deg, #c2c9cc 0%, #6d99b9 100%);
  border-radius: 0.08rem;
  color: #fff;
  font-size: 0.32rem;
  text-align: center;
  height: 0.88rem;
  line-height: 0.88rem;
  margin-top: 0.1rem;
}
.login .tip {
  text-align: center;
  line-height: 0.34rem;
}
.login .tip a,
.login .tip i {
  color: #9b9b9b;
  font-size: 0.24rem;
  vertical-align: top;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.3);
}
.mask2 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.7);
}
.tipinfo {
  position: fixed;
  width: 91.5%;
  height: 82.78%;
  top: 7.62%;
  left: 4.25%;
  z-index: 2;
  border-radius: 8px;
  background-color: #fff;
}

.tipinfo .tipinfo_head {
  background-color: #428adf;
  height: 0.92rem;
  line-height: 0.92rem;
  color: #fff;
  border-radius: 8px 8px 0 0;
  padding-left: 0.3rem;
}
.tipinfo .tipinfo_head .title {
  font-size: 0.28rem;
  float: left;
  width: 76.27365356622999%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tipinfo .tipinfo_head .cancel {
  float: right;
  color: #428adf;
  background-color: #fff;
  border-radius: 50px;
  width: 0.96rem;
  height: 0.56rem;
  text-align: center;
  line-height: 0.56rem;
  margin-top: 0.18rem;
  font-size: 0.44rem;
}

.tipinfo .tipinfo_content {
  position: absolute;
  top: 0.92rem;
  bottom: 0;
  left: 0;
  right: 0;
}
.tipinfo .tipinfo_content p {
  font-size: 0.38rem;
  left: 0;
  right: 0;
  padding: 10px;
}
.btn-disabled {
  background: #ccc !important;
  color: #fff !important;
  border-color: #ccc !important;
}
.c-regInfo {
  position: fixed;
  width: 84%;
  /* height: 50%; */
  top: 25%;
  left: 8%;
  border-radius: 0.5rem;
  background: #fff;
  z-index: 2;
}
.reg-head {
  width: 100%;
  height: 2rem;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  background: #428adf;
  position: relative;
}
.reg-img {
  width: 2rem;
  height: 3rem;
  position: absolute;
  margin-left: 50%;
  left: -1rem;
  margin-top: -1.5rem;
}
.reg-img img {
  width: 100%;
}
.reg-tip {
  width: 100%;
  height: 0.8rem;
  line-height: 0.8rem;
  overflow: hidden;
  color: #fff;
  position: absolute;
  bottom: 0.1rem;
  text-align: center;
  font-size: 0.35rem;
}
.reg-line {
  display: flex;
  font-size: 0.35rem;
}
.reg-line .reg-label {
  width: 35%;
  height: 0.8rem;
  line-height: 1rem;
  padding-left: 10%;
}
.reg-line .reg-txt {
  width: 65%;
  height: 0.8rem;
  line-height: 0.8rem;
}
.reg-button {
  margin-top: 0.2rem;
  width: 100%;
  height: 1rem;
  display: flex;
  justify-content: space-around;
}
.reg-button div {
  width: 2rem;
  height: 0.8rem;
  line-height: 0.8rem;
  border-radius: 0.2rem;
  text-align: center;
  font-size: 0.3rem;
  color: white;
}
.reg-button-left {
  background: gray;
}
.reg-button-right {
  background: #018bf0;
}
.reg-remind {
  line-height: 0.44rem;
  text-align: center;
  font-size: 0.2rem;
  color: red;
}
#cardtype {
  /* appearance:menulist; */
  /* -webkit-appearance:menulist !important; */
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  border: solid 1px #cccccc;
  /* background: url(../assets/img/sanjiaoxing.png) no-repeat scroll 95% center transparent; */
  background-size: 7%;
  padding-right: 34px;
  width: 100%;
  height: 30px;
  border-radius: 5px;
  padding-left: 5px;
}
select::-ms-expand {
  display: none;
}

.radioDiv {
  width: 100%;
  height: 1.6rem;
}
.radioBox {
  width: 100%;
  height: 50%;
  display: flex;
}
.greenDiv {
  width: 20%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.textNow {
  width: 80%;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 10px;
}
.Smear {
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
  background: #409eff;
  border: 1px solid #409eff;
}
.full {
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
  background: white;
  border: 1px solid #409eff;
}
</style>

<style>
.diaLog .el-dialog__header {
  padding: 0 !important;
}
.diaLog .el-dialog--center .el-dialog__body {
  padding: 15px 10px !important;
}

.user_birthday .weui-cells:before {
  border-top: 0 !important;
}
.user_birthday .weui-cell {
  padding: 10px 0 !important;
}
.user_birthday .vux-cell-placeholder {
  color: #dacfcc;
  font-size: 0.32rem;
}
.user_birthday .vux-cell-value {
  color: #4a4a4a !important;
  font-size: 0.32rem;
}
</style>
