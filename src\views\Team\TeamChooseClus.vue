<template>
  <div class="hosp-choose">
    <div class="title">请选择体检套餐</div>
    <van-cell-group>
      <van-cell
        v-for="(item, index) in teamInfoList"
        :key="index"
        center
        class="cell"
        :title="item.clus_name"
      >
        <template #title>
          <div class="CardTitle" @click="toTeamBookSum(item)">
            <div class="titleImg">
              <img src="../../assets/detailsSex01.png" alt="套餐详情" />
            </div>

            <div class="CardText">
              <span>{{ item.clus_name }}</span>
            </div>

            <div class="CardSee">
              <span>查看详情</span>
              <span style="margin-top: 4.1px">
                <img src="../../assets/Nextpage.png" alt="查看详情" />
              </span>
            </div>
          </div>
        </template>
      </van-cell>
    </van-cell-group>
  </div>
</template>

<script>
import { storage } from "../../common";
import { Cell, CellGroup } from "vant";

export default {
  components: {
    "van-cell": Cell,
    "van-cell-group": CellGroup,
  },
  data() {
    return {
      teamInfoList: [],
    };
  },
  created() {
    this.teamInfoList = JSON.parse(storage.session.get("teamInfo"));
  },
  methods: {
    toTeamBookSum(item) {
      item.lnc_name = JSON.parse(storage.session.get("lncInfo")).lnc_name;
      storage.session.set("teamInfo", JSON.stringify([item]));
      this.$router.push({
        path: "/TeamBookSum"
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.hosp-choose {
  .title {
    font-size: 0.36rem;
    text-align: center;
    background: #fff;
    padding: 0.2rem;
  }
  .cell {
    font-size: 0.32rem;
  }

  .CardTitle {
    width: 96%;
    height: 1.1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 4%;
  }

  .CardTitle .titleImg {
    width: 0.56rem;
    height: 0.56rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .CardText {
    width: calc(100% - 2.52rem);
    height: 1rem;
    display: flex;
    align-items: center;
    /* margin-left: .2rem; */
    font-size: 0.32rem;
    color: #4a4a4a;
    letter-spacing: -0.02px;
    font-weight: 600;
  }

  .CardText span {
    margin-left: 0.2rem;
  }

  .CardSee {
    width: 1.96rem;
    height: 1rem;
    color: #6a9be4;
    letter-spacing: -0.01px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .CardSeeImg {
    width: 0.64rem;
    height: 0.64rem;
  }

  .icon-Nextpage {
    margin-top: 3px;
    font-size: 0.5rem;
  }
}
</style>
