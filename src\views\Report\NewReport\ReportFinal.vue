<template>
  <div id="reportWrap">
    <!--头部部分-->
    <div class="header">
      <img src="../../../assets/report/user_pr.png" alt />
      <h2>{{report.name}}</h2>
      <span>{{(report.sex==1?"男":"女")+" "+report.age+"岁"}}</span>
    </div>
    <!--内容部分-->
    <div class="content">
      <div class="content_top">
        <img src="../../../assets/report/<EMAIL>" alt />
        <h2>当前体检报告</h2>
        <span>体检编号：{{report.regno}}</span>
        <p>{{report.reg_date}}</p>
      </div>
      <hr />
      <div class="content_bottom" v-if="sumuplist.length>0">
        <img src="../../../assets/report/<EMAIL>" alt />
        <h1>
          <strong>总检报告</strong>
        </h1>
        <p>
          <strong v-for="(item,index) in sumuplist" :key="index">
            {{item}}
            <br />
          </strong>
        </p>
      </div>
      <div class="nothing" v-if="sumuplist.length<1">
        <div>
          <img src="../../../assets/report/nothing.png" />
          <span>没有数据</span>
        </div>
      </div>
    </div>
    <!--网页尾部-->
    <div class="footer">
      <div class="footer_left" @click="gotourl('/ReportMian')">
        <img src="../../../assets/report/<EMAIL>" alt />
        <span>报告首页</span>
      </div>
      <div class="footer_right" @click="gotourl('/ReportSuggest')">
        <span>医生建议</span>
        <img src="../../../assets/report/<EMAIL>" alt />
      </div>
    </div>
  </div>
</template>

<script>
import { dataUtils, ajax, storage } from '@/common'
import { toolsUtils } from '@/common'
import apiUrls from '@/config/apiUrls';
export default {
  components: {
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      report: {},
      sumuplist: [],
    };
  },
  created() {
    try {
      this.report = JSON.parse(storage.session.get('report'));
    } catch (error) {

    }
    //console.log(storage.session.get('report'));
    this.disposeRes()
  },
  mounted() { },
  methods: {
    gotourl(url) {
      this.$router.push({ path: url });
    },
    disposeRes() {
      var res = this.report;
      this.sumuplist = [];
      //综述
      if (res.sumup.indexOf('\r\n') != -1)
        this.sumuplist = res.sumup.split('\r\n');
      else
        this.sumuplist.push(res.sumup);
      //遍历删除空元素
      for (var i = 0; i < this.sumuplist.length; i++) {
        if (this.sumuplist[i] === "") {
          this.sumuplist.splice(i, 1);//删除一个元素
          i--;
        }
      }
    }
  },
  computed: {
    newtitle: function () {
      return this.title;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
p {
  margin: 0;
}
h1,
h2,
h3,
h4,
h5 {
  margin: 0;
}
.nothing {
  width: 100%;
}
.nothing div {
  width: 60%;
  margin: 10% auto 0;
}
.nothing div span {
  width: 100%;
  display: block;
  text-align: center;
  font-size: 0.48rem;
  color: #018bf0;
}
#reportWrap {
  width: 100%;
  height: 100%;
  background-color: #f5f6f7;
}
/*头部部分*/
#reportWrap .header {
  width: 100%;
  height: 1.44rem;
  position: relative;
  background: #ffffff;
  box-shadow: 0 1px 8px 0 #dfdfdf;
}
#reportWrap .header > img {
  width: 0.96rem;
  height: 0.96rem;
  display: inline-block;
  position: absolute;
  left: 0.28rem;
  top: 0.24rem;
}
#reportWrap .header > h2 {
  display: inline-block;
  font-family: PingFangSC-Regular;
  font-size: 0.34rem;
  color: #4a4a4a;
  width: 1.36rem;
  height: 0.48rem;
  position: absolute;
  top: 0.26rem;
  left: 1.56rem;
}
#reportWrap .header > span {
  display: inline-block;
  width: 1.16rem;
  height: 0.42rem;
  font-family: PingFangSC-Regular;
  font-size: 0.3rem;
  color: #9e9e9e;
  position: absolute;
  top: 0.74rem;
  left: 1.56rem;
}
#reportWrap .header > .toggle {
  display: inline-block;
  border: 1px solid #ef809b;
  border-radius: 100px;
  width: 1.8rem;
  height: 0.52rem;
  text-align: center;
  line-height: 0.52rem;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #f64772;
  position: absolute;
  top: 0.42rem;
  left: 5.4rem;
}
#reportWrap .header > .toggle > p {
  display: inline-block;
}
#reportWrap .header > .toggle > img {
  display: inline-block;
  width: 0.17rem;
  height: 0.17rem;
}
/*内容部分*/
.content {
  width: 100%;
}
.content .content_top {
  width: 7.5rem;
  height: 1.4rem;
}
.content .content_top > img {
  width: 0.56rem;
  height: 0.64rem;
  display: inline-block;
  position: absolute;
  top: 1.94rem;
  left: 0.32rem;
}
.content .content_top > h2 {
  font-size: 14px;
  color: #646a6f;
  position: absolute;
  top: 1.86rem;
  left: 1.08rem;
  margin: 0;
}
.content .content_top > span {
  position: absolute;
  top: 2.3rem;
  left: 1.08rem;
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #646a6f;
}
.content .content_top > p {
  position: absolute;
  left: 5.5rem;
  top: 2.3rem;
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #646a6f;
}
.content hr {
  width: 7.02rem;
  height: 0.02rem;
  margin: 0 auto;
  border: 0;
  padding: 0;
  background-color: #dfdfdf;
}
.content .content_bottom {
  width: 100%;
  height: 100%;
  background: #ffffff;
}
.content .content_bottom > img {
  display: inline-block;
  width: 0.39rem;
  height: 0.36rem;
  position: absolute;
  top: 3.18rem;
  left: 0.34rem;
}
.content .content_bottom > h1 {
  font-family: PingFangSC-Regular;
  font-size: 0.32rem;
  color: #646a6f;
  display: inline-block;
  position: absolute;
  top: 3.18rem;
  left: 0.924rem;
  margin: 0;
}
.content .content_bottom > p {
  font-family: PingFangSC-Regular;
  font-size: 0.28rem;
  color: #737a80;
  line-height: 24px;
  display: inline-block;
  width: 6.96rem;
  position: absolute;
  top: 3.74rem;
  left: 0.34rem;
  padding-bottom: 1.58rem;
}
.footer {
  width: 7.5rem;
  height: 0.88rem;
  position: fixed;
  bottom: 0;
  left: 0;
}
.footer .footer_left {
  width: 3.73rem;
  height: 0.88rem;
  float: left;
  border-right: 1px solid white;
  background-color: #489eea;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.footer .footer_right {
  width: 3.73rem;
  height: 0.88rem;
  float: left;
  background-color: #489eea;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.footer .footer_left img {
  width: 0.356rem;
  height: 0.304rem;
  display: inline-block;
}
.footer .footer_left span {
  font-family: PingFangSC-Medium;
  display: inline-block;
  /* width: 1.28rem; */
  height: 0.44rem;
  line-height: 0.44rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.footer .footer_right img {
  width: 0.356rem;
  height: 0.304rem;
  display: inline-block;
  margin-left: 0.2rem;
}
.footer .footer_right span {
  font-family: PingFangSC-Medium;
  display: inline-block;
  /* width: 1.28rem; */
  height: 0.44rem;
  line-height: 0.44rem;
  font-size: 0.32rem;
  color: #ffffff;
}
</style>