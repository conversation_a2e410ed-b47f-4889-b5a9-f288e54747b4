<template>
  <div class="main">
    <van-form ref="form">
      <header>
        <p>健康体检自测问卷(试行)</p>
        <p>
          非常感谢您填写健康体检问卷，本问卷目的在于了解您与健康相关的状况与影响因素,进而指导您精准预防。我们充分尊重您的个人信息隐私权,任何个人或机构未经许可或授权不得获取您的个人敏感信息。请您认真回答每个问题,并在“口”相应处打√。
        </p>
      </header>

      <div class="content">
        <div class="content-item">
          <p class="title">{{ questionnaireData[0].title }}</p>
          <div class="content-choice">
            <p>1.{{ questionnaireData[0].children[8].issue }}</p>
            <div class="">
              <van-field
                placeholder="在此请填写"
                v-model="questionnaireData[0].children[8].value"
                :rules="[{ required: true, message: '请填写姓名' }]"
              />
            </div>
            <p v-if="isShowIdCard" style="text-indent: 16px">
              {{ questionnaireData[0].children[9].issue }}
            </p>
            <div class="" v-if="isShowIdCard">
              <van-field
                placeholder="在此请填写"
                v-model="questionnaireData[0].children[9].value"
                :rules="[{ required: true, message: '请填写身份证' }]"
              />
            </div>

            <p>2.{{ questionnaireData[0].children[0].issue }}</p>
            <div class="">
              <van-radio-group
                v-model="questionnaireData[0].children[0].value"
                shape="square"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[0].children[0]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[0].children[0].value"
                :rules="[{ required: true, message: '请选择性别' }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>3.{{ questionnaireData[0].children[1].issue }}</p>
            <div class="">
              <van-field
                placeholder="在此请填写"
                v-model="questionnaireData[0].children[1].value"
                :rules="[{ required: true, message: '请填写民族' }]"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>4.{{ questionnaireData[0].children[2].issue }}</p>
            <div class="">
              <van-field
                v-model="questionnaireData[0].children[2].value"
                @click="questionnaireData[0].children[2].popup.show = true"
                placeholder="点击选择"
                readonly
                :rules="[{ required: true, message: '请选择出生日期' }]"
              />
              <van-popup
                v-model="questionnaireData[0].children[2].popup.show"
                position="bottom"
              >
                <van-datetime-picker
                  v-model="questionnaireData[0].children[2].popup.date"
                  title="选择日期"
                  type="date"
                  :min-date="date.minDate"
                  :max-date="date.maxDate"
                  @confirm="dateConfirm"
                  @cancel="questionnaireData[0].children[2].popup.show = false"
                />
              </van-popup>
            </div>
          </div>
          <div class="content-choice">
            <p>5.{{ questionnaireData[0].children[3].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[0].children[3].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[0].children[3]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[0].children[3].value"
                :rules="[{ required: true, message: '请选择文化程度' }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>6.{{ questionnaireData[0].children[4].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[0].children[4].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[0].children[4]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[0].children[4].value"
                :rules="[{ required: true, message: '请选择婚姻状况' }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>7.{{ questionnaireData[0].children[5].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[0].children[5].value"
              >
                <div
                  v-for="(item, index) in questionnaireData[0].children[5]
                    .items"
                  :key="index"
                >
                  <van-radio :name="item.label" v-if="item.label != '其他'">{{
                    item.label
                  }}</van-radio>
                  <div class="other" v-else>
                    <van-radio :name="item.label">其他</van-radio
                    ><span>请注明:</span>
                    <van-field
                      style="flex: 1"
                      v-model="questionnaireData[0].children[5].remarks"
                      placeholder="在此处填写"
                      :readonly="
                        questionnaireData[0].children[5].value == '其他'
                          ? false
                          : true
                      "
                    />
                  </div>
                </div>
              </van-radio-group>
              <van-field
                v-model="questionnaireData[0].children[5].value"
                :rules="[{ required: true, message: '请选择职业' }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>8.{{ questionnaireData[0].children[6].issue }}</p>
            <div class="" style="display: flex; flex-direction: column">
              <van-field
                v-model="questionnaireData[0].children[6].provinceAndCity"
                @click="questionnaireData[0].children[6].show = true"
                placeholder="点击选择省/市/区或县"
                readonly
                :rules="[{ required: true, message: '请选择省/市/区或县' }]"
              />

              <div>
                <van-field
                  v-model="questionnaireData[0].children[6].address"
                  placeholder="输入详细地址"
                  :rules="[{ required: true, message: '请输入详细地址' }]"
                  label-width="65px"
                  label="详细地址:"
                />
                <van-field
                  v-model="questionnaireData[0].children[6].durationOfResidence"
                  placeholder="居住时长(单位:年)"
                  type="number"
                  :rules="[{ required: true, message: '请输入居住时长' }]"
                  label-width="65px"
                  label="居住时长:"
                />
              </div>
              <van-popup
                v-model="questionnaireData[0].children[6].show"
                position="bottom"
              >
                <van-area
                  :area-list="areaList"
                  @cancel="questionnaireData[0].children[6].show = false"
                  @confirm="familyConfirm"
                />
              </van-popup>
            </div>
          </div>
          <div class="content-choice">
            <p>9.{{ questionnaireData[0].children[7].issue }}</p>
            <div class="" style="display: flex; flex-direction: column">
              <van-field
                v-model="questionnaireData[0].children[7].provinceAndCity"
                @click="questionnaireData[0].children[7].show = true"
                placeholder="点击选择省/市/区或县"
                readonly
                :rules="[{ required: true, message: '请选择省/市/区或县' }]"
              />

              <div>
                <van-field
                  v-model="questionnaireData[0].children[7].address"
                  placeholder="输入详细地址"
                  style="flex: 6"
                  :rules="[{ required: true, message: '请输入详细地址' }]"
                  label-width="65px"
                  label="详细地址:"
                />
                <van-field
                  v-model="questionnaireData[0].children[7].durationOfResidence"
                  placeholder="居住时长(单位:年)"
                  type="number"
                  :rules="[{ required: true, message: '请输入居住时长' }]"
                  style="flex: 4"
                  label-width="65px"
                  label="居住时长:"
                />
              </div>
              <van-popup
                v-model="questionnaireData[0].children[7].show"
                position="bottom"
              >
                <van-area
                  :area-list="areaList"
                  @cancel="questionnaireData[0].children[7].show = false"
                  @confirm="companyConfirm"
                />
              </van-popup>
            </div>
          </div>
        </div>
        <div class="content-item">
          <p class="title">{{ questionnaireData[1].title }}</p>
          <div class="content-choice">
            <p>10.{{ questionnaireData[1].children[0].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[1].children[0].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[1].children[0]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[1].children[0].value"
                :rules="[{ required: true, message: '请选择健康状况' }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>11.{{ questionnaireData[1].children[3].issue }}</p>
            <div class="">
              <van-field
                v-model="questionnaireData[1].children[3].value"
                type="number"
                :rules="[{ required: true, message: '请给出具体打分' }]"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>12.{{ questionnaireData[1].children[4].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[1].children[4].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[1].children[4]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[1].children[4].value"
                :rules="[
                  {
                    required: true,
                    message: '请选择未来 10 年患心血管疾病的风险',
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>13.{{ questionnaireData[1].children[1].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[1].children[1].value"
              >
                <van-radio name="无">无</van-radio>
                <van-radio name="有">有</van-radio>
              </van-radio-group>
              <van-field
                v-model="questionnaireData[1].children[1].value"
                :rules="[{ required: true, message: '请回答有无手术史' }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
            <div class="operation-item">
              <div>
                <span>输血史：</span>
                <van-radio-group
                  shape="square"
                  v-model="questionnaireData[1].children[1].bloodTransfusion"
                  direction="horizontal"
                >
                  <van-radio name="无">无</van-radio>
                  <van-radio name="有">有</van-radio>
                </van-radio-group>
                <van-field
                  v-model="questionnaireData[1].children[1].bloodTransfusion"
                  :rules="[{ required: true, message: '请回答有输血史' }]"
                  style="opacity: 0; width: 0; height: 0"
                />
              </div>
              <div>
                <span>药物过敏史：</span>
                <van-field
                  style="flex: 1"
                  placeholder="在此处填写，没有填写无"
                  v-model="questionnaireData[1].children[1].allergyL"
                  :rules="[
                    {
                      required: true,
                      message: '请填写药物过敏史,(没有填写无)',
                    },
                  ]"
                />
              </div>
            </div>
          </div>
          <div class="content-choice">
            <p>14.{{ questionnaireData[1].children[2].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[1].children[2].value"
              >
                <van-radio name="无">无</van-radio>
                <van-radio name="有">有</van-radio>
              </van-radio-group>
              <van-field
                v-model="questionnaireData[1].children[2].value"
                :rules="[
                  {
                    required: true,
                    message: `请回答:${questionnaireData[1].children[2].issue}`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
            <div
              class="disease"
              v-show="questionnaireData[1].children[2].value == '有'"
            >
              <div
                class="disease-item"
                v-for="(item, index) in questionnaireData[1].children[2].items"
                :key="index"
              >
                <div class="disease-item-head">
                  <van-checkbox v-model="item.check" shape="square" />
                  <span>{{ item.diseaseName }}</span>
                  <van-field
                    v-if="item.remarks !== undefined && item.remarks !== null"
                    placeholder="在此处填写"
                    v-model="item.remarks"
                    style="flex: 1"
                    :readonly="item.check ? false : true"
                  />
                </div>
                <div class="disease-item-body" v-show="item.check">
                  <van-field
                    label-width="100px"
                    label="患病时长(年)："
                    placeholder="在此处填写"
                    v-model="item.duration"
                    type="number"
                    v-if="!item.special"
                  />
                  <van-checkbox
                    v-if="!item.special"
                    v-model="item.medication"
                    shape="square"
                    >已经用药</van-checkbox
                  >
                  <div class="medicalAdvice" v-if="!item.special">
                    <span>遵医嘱按时服药</span>
                    <van-radio-group
                      v-model="item.medicalAdvice"
                      shape="square"
                      direction="horizontal"
                    >
                      <van-radio name="是">是</van-radio>
                      <van-radio name="否">否</van-radio>
                    </van-radio-group>
                  </div>
                  <van-checkbox v-model="item.familyHistory" shape="square">
                    家族史（父母亲及兄弟姐妹）</van-checkbox
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content-item">
          <p class="title">{{ questionnaireData[2].title }}</p>
          <div class="content-choice">
            <p>15.{{ questionnaireData[2].children[0].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[0].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[2].children[0]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[0].value"
                :rules="[{ required: true, message: `请回答:您吸烟吗?` }]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>16.{{ questionnaireData[2].children[1].issue }}</p>
            <div class="">
              <van-field
                type="number"
                v-model="questionnaireData[2].children[1].lengthAsASmoker"
                style="flex: 5"
                placeholder="烟龄"
                :rules="[{ required: true, message: `请输入烟龄` }]"
              />
              <van-field
                type="number"
                v-model="questionnaireData[2].children[1].quantity"
                style="flex: 5"
                placeholder="平均每日吸烟数"
                :rules="[{ required: true, message: `请输入每日吸烟数` }]"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>17.{{ questionnaireData[2].children[2].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[2].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[2].children[2]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[2].value"
                :rules="[
                  {
                    required: true,
                    message: `请回答:${questionnaireData[2].children[2].issue}`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>18.{{ questionnaireData[2].children[3].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[3].value"
              >
                <div
                  v-for="(item, index) in questionnaireData[2].children[3]
                    .items"
                  :key="index"
                >
                  <van-radio :name="item.label" v-if="item.label != '其他'">{{
                    item.label
                  }}</van-radio>

                  <div class="other" v-else>
                    <van-radio :name="item.label">{{ item.label }}</van-radio>
                    <van-field
                      style="flex: 1"
                      placeholder="在此处填写"
                      v-model="questionnaireData[2].children[3].remarks"
                      :readonly="
                        questionnaireData[2].children[3].value == '其他'
                          ? false
                          : true
                      "
                    />
                    <!-- <span>(不饮酒者跳过)</span> -->
                  </div>
                </div>
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[3].value"
                :rules="[
                  {
                    required: true,
                    message: `请回答:${questionnaireData[2].children[3].issue}`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>19.{{ questionnaireData[2].children[4].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[4].value"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[2].children[4]
                    .items"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[4].value"
                :rules="[
                  {
                    required: true,
                    message: `请回答:${questionnaireData[2].children[4].issue}`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
            <div style="display: flex; flex-direction: column">
              <p>且口味偏向于:</p>
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[4].flavor"
              >
                <van-radio
                  :name="item.label"
                  v-for="(item, index) in questionnaireData[2].children[4]
                    .items1"
                  :key="index"
                  >{{ item.label }}</van-radio
                >
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[4].flavor"
                :rules="[
                  {
                    required: true,
                    message: `请回答：且口味偏向于`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>20.{{ questionnaireData[2].children[5].issue }}</p>
            <div class="">
              <van-field
                type="number"
                style="flex: 5"
                v-model="questionnaireData[2].children[5].night"
                placeholder="夜间睡眠时长"
                :rules="[{ required: true, message: `请输入夜间睡眠时长` }]"
              />
              <van-field
                type="number"
                style="flex: 5"
                v-model="questionnaireData[2].children[5].noon"
                placeholder="午睡时长"
                :rules="[{ required: true, message: `请输入午睡时长` }]"
              />
            </div>
            <div>
              <span>睡眠质量：</span>
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[5].sleepQuality"
                direction="horizontal"
              >
                <van-radio name="较好">较好</van-radio>
                <van-radio name="一般">一般</van-radio>
                <van-radio name="较差">较差</van-radio>
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[5].sleepQuality"
                :rules="[
                  {
                    required: true,
                    message: `请回答:${questionnaireData[2].children[5].issue}`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>21.{{ questionnaireData[2].children[6].issue }}</p>
            <div class="">
              <van-radio-group
                shape="square"
                v-model="questionnaireData[2].children[6].value"
                style="width: 100%"
              >
                <div class="other">
                  <van-radio name="是">是</van-radio><span>（绝经年龄:</span>
                  <van-field
                    style="flex: 1"
                    placeholder="在此处填写"
                    type="number"
                    v-model="questionnaireData[2].children[6].menopause"
                    :readonly="
                      questionnaireData[2].children[6].value == '是'
                        ? false
                        : true
                    "
                  />岁）
                </div>
                <van-radio name="否">否</van-radio>
              </van-radio-group>
              <van-field
                v-model="questionnaireData[2].children[6].value"
                v-if="questionnaireData[0].children[0].value == '女'"
                :rules="[
                  {
                    required: true,
                    message: `请回答:${questionnaireData[2].children[6].issue}`,
                  },
                ]"
                style="opacity: 0; width: 0; height: 0"
              />
            </div>
          </div>
          <div class="content-choice">
            <p>22.{{ questionnaireData[2].children[7].issue }}</p>
            <div class="" style="display: flex; flex-direction: column">
              <van-checkbox
                style="margin-bottom: 10px"
                v-model="item.check"
                v-for="(item, index) in questionnaireData[2].children[7].items"
                :key="index"
                shape="square"
                >{{ item.title }}</van-checkbox
              >
            </div>
          </div>
        </div>
        <div class="content-item">
          <p class="title">{{ questionnaireData[3].title }}</p>
          <div
            v-for="(item, index) in questionnaireData[3].children"
            :key="index"
          >
            <div
              class="content-choice"
              v-if="item.value !== undefined && item.value !== null"
            >
              <p>{{ index + 23 }}.{{ item.issue }}</p>
              <div class="">
                <van-radio-group shape="square" v-model="item.value">
                  <van-radio name="无相关体力活动">无相关体力活动</van-radio>
                  <van-radio name="有相关体力活动">有相关体力活动</van-radio>
                  <!-- <van-radio name="3~5次/周">3~5次/周</van-radio>
                  <van-radio name="＞5次周">＞5次周</van-radio> -->
                </van-radio-group>
                <van-field
                  v-model="item.value"
                  :rules="[{ required: true, message: `请回答:${item.issue}` }]"
                  style="opacity: 0; width: 0; height: 0"
                />
              </div>
              <div class="motion" v-if="item.value == '有相关体力活动'">
                <span>每周</span>
                <van-field
                  style="flex: 1"
                  placeholder=""
                  v-model="item.day"
                  :rules="[
                    {
                      required: true,
                      message: '请填写天数',
                    },
                  ]"
                />
                <span>天，每天</span>
                <van-field
                  style="flex: 1"
                  placeholder=""
                  v-model="item.hour"
                  :rules="[
                    {
                      required: true,
                      message: '请填写小时',
                    },
                  ]"
                />
                <span>小时</span>
                <van-field
                  style="flex: 1"
                  placeholder=""
                  v-model="item.minute"
                  :rules="[
                    {
                      required: true,
                      message: '请填写分钟',
                    },
                  ]"
                />
                <span>分钟</span>
              </div>
            </div>
            <div class="content-choice" v-else>
              <p>{{ index + 23 }}.{{ item.issue }}</p>
              <div class="">
                <van-field
                  v-model="item.hour"
                  type="number"
                  style="flex: 5"
                  placeholder="小时"
                  :rules="[{ required: true, message: `请输入小时` }]"
                />
                <van-field
                  v-model="item.minute"
                  type="number"
                  style="flex: 5"
                  placeholder="分钟"
                  :rules="[{ required: true, message: `请输入分钟` }]"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content-item">
          <p class="title">{{ questionnaireData[4].title }}</p>
          <div
            v-for="(item, index) in questionnaireData[4].children"
            :key="index"
          >
            <div class="content-choice" v-if="item.items">
              <p>{{ index + 27 }}.{{ item.issue }}</p>
              <div class="">
                <van-radio-group shape="square" v-model="item.value">
                  <van-radio
                    :name="i.label"
                    v-for="(i, index) in item.items"
                    :key="index"
                    >{{ i.label }}</van-radio
                  >
                </van-radio-group>
                <van-field
                  v-model="item.value"
                  :rules="[{ required: true, message: `请回答:${item.issue}` }]"
                  style="opacity: 0; width: 0; height: 0"
                />
              </div>
            </div>
            <div class="content-choice" v-else>
              <p>{{ index + 27 }}.{{ item.issue }}</p>
              <div class="">
                <van-radio-group
                  v-model="item.value"
                  shape="square"
                  style="width: 100%"
                >
                  <van-radio name="没有">没有</van-radio>
                  <div class="other">
                    <van-radio name="有">有</van-radio><span>，每周</span>
                    <van-field
                      v-if="item.value == '有'"
                      style="width: 70px"
                      placeholder="请填写"
                      type="number"
                      :readonly="item.value == '有' ? false : true"
                      :rules="[
                        {
                          required: true,
                          message: '请填写天数',
                        },
                      ]"
                      v-model="item.remarks"
                    />
                                   
                    <van-field
                      v-else
                      style="width: 70px"
                      placeholder="请填写"
                      type="number"
                      :readonly="item.value == '有' ? false : true"
                      v-model="item.remarks"
                    />天
                  </div>
                  <van-radio name="不知道">不知道</van-radio>
                </van-radio-group>
                <van-field
                  v-model="item.value"
                  :rules="[{ required: true, message: `请回答:${item.issue}` }]"
                  style="opacity: 0; width: 0; height: 0"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          style="
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 30px;
          "
        >
          <van-button style="width: 50%" type="primary" @click="submit"
            >提交</van-button
          >
        </div>
      </div>
    </van-form>
  </div>
</template>

<script>
// import res from "./data.json";
import { storage, ajax } from "../../common";
import apiUrils from "../../config/apiUrls";
import { Toast } from "vant";
// import { showDialog } from "vant";
import { areaList } from "@vant/area-data";

export default {
  name: "question",
  data() {
    return {
      areaList,
      head: {
        organizationName: "",
        medicalExaminationCode: "",
      },
      date: {
        minDate: new Date(this.getDateFromYearsAgoOrAfter(-100)),
        maxDate: new Date(this.getDateFromYearsAgoOrAfter(100)),
      },
      questionnaireData: [
        {
          title: "个人基本信息",
          children: [
            {
              issue: "性别",
              value: "",
              items: [
                {
                  label: "男",
                  value: "0",
                },
                {
                  label: "女",
                  value: "1",
                },
              ],
            },
            {
              issue: "民族",
              value: "",
            },
            {
              issue: "出生日期",
              value: "",
              popup: {
                show: false,
                date: new Date("2000/01/01"),
              },
            },
            {
              issue: "文化程度",
              value: "",
              items: [
                {
                  label: "研究生及以上",
                  value: "0",
                },
                {
                  label: "大学本科或专科",
                  value: "1",
                },
                {
                  label: "初中、高中、中专、技校",
                  value: "2",
                },
                {
                  label: "小学及以下",
                  value: "3",
                },
              ],
            },
            {
              issue: "婚姻状况",
              value: "",
              items: [
                {
                  label: "未婚",
                  value: "0",
                },
                {
                  label: "已婚",
                  value: "1",
                },
                {
                  label: "离异",
                  value: "2",
                },
                {
                  label: "丧偶",
                  value: "4",
                },
                {
                  label: "其他",
                  value: "5",
                },
              ],
            },
            {
              issue: "职业",
              value: "",
              remarks: "",
              items: [
                {
                  label: "国家公务员",
                  value: "0",
                },
                {
                  label: "专业技术人员",
                  value: "1",
                },
                {
                  label: "企业管理人员",
                  value: "2",
                },
                {
                  label: "工人",
                  value: "4",
                },
                {
                  label: "农民",
                  value: "5",
                },
                {
                  label: "学生",
                  value: "6",
                },
                {
                  label: "现役军人",
                  value: "7",
                },
                {
                  label: "自由职业者",
                  value: "8",
                },
                {
                  label: "个体经营者",
                  value: "9",
                },
                {
                  label: "无业人员",
                  value: "10",
                },
                {
                  label: "退(离)休人员",
                  value: "11",
                },
                {
                  label: "其他",
                  value: "12",
                },
              ],
            },
            {
              issue: "现家庭住址",
              provinceAndCity: "",
              address: "",
              durationOfResidence: "",
              show: false,
            },
            {
              issue: "工作单位所在地",
              provinceAndCity: "",
              address: "",
              durationOfResidence: "",
              show: false,
            },
            {
              issue: "姓名",
              value: "",
            },
            {
              issue: "身份证",
              value: "",
            },
          ],
        },
        {
          title: "健康状况及家族史",
          children: [
            {
              issue: "近一年内，您觉得您的健康状况怎么样?",
              value: "",
              items: [
                {
                  label: "很好",
                  value: "0",
                },
                {
                  label: "好",
                  value: "1",
                },
                {
                  label: "一般",
                  value: "2",
                },
                {
                  label: "不好",
                  value: "3",
                },
                {
                  label: "很不好",
                  value: "4",
                },
              ],
            },
            {
              issue: "本人手术史",
              value: "",
              bloodTransfusion: "",
              allergyL: "",
            },
            {
              issue: "本人疾病史",
              value: "",
              items: [
                {
                  check: false,
                  diseaseName: "糖尿病",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "高血压",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "血脂异常",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "心脏病",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "心肌梗死",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "心房纤颤/心房扑动",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "脑血管病",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "脑卒中史（或家族史）",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName:
                    "早发心脑血管病家族史（男性小于55岁，女性小于65岁）",
                  familyHistory: false,
                  special: true,
                },
                {
                  check: false,
                  diseaseName: "恶性肿瘤",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "肾功能异常",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "自身免疫性疾病",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "视力下降或视物不清",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "间歇性跛行",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                },
                {
                  check: false,
                  diseaseName: "其他疾病",
                  duration: "",
                  medication: false,
                  medicalAdvice: "",
                  familyHistory: false,
                  remarks: "",
                },
              ],
            },
            {
              issue:
                "您认为未来10年您患心血管疾病的风险为多少？ (以0～10表示 ，0代表 绝对不可能发生 ，10代表绝对发生)请给出具体打分",
              value: "",
            },
            {
              issue:
                "与同年龄、同性别的健康人群相比 ，您认为未来 10 年患心血管疾病的风险",
              value: "",
              items: [
                {
                  label: "远低于",
                  value: "0",
                },
                {
                  label: "低于",
                  value: "1",
                },
                {
                  label: "等于",
                  value: "2",
                },
                {
                  label: "高于",
                  value: "3",
                },
                {
                  label: "远高于",
                  value: "4",
                },
              ],
            },
          ],
        },
        {
          title: "生活方式信息",
          children: [
            {
              issue: "您吸烟吗?",
              value: "",
              items: [
                {
                  label: "吸烟",
                  value: "0",
                },
                {
                  label: "不吸烟但有被动吸烟",
                  value: "1",
                },
                {
                  label: "不吸烟且没有被动吸烟",
                  value: "2",
                },
                {
                  label: "已戒烟",
                  value: "3",
                },
              ],
            },
            {
              issue: "现在/戒烟(6个月以上)前，您已经吸烟__年，每日平均__支",
              lengthAsASmoker: "",
              quantity: "",
            },
            {
              issue: "你过去一年饮酒?",
              value: "",
              items: [
                {
                  label: "不饮酒",
                  value: "0",
                },
                {
                  label: "已戒酒",
                  value: "1",
                },
                {
                  label: "每月少于一次",
                  value: "2",
                },
                {
                  label: "每月1~10次",
                  value: "3",
                },
                {
                  label: "每月超过10次",
                  value: "4",
                },
              ],
            },
            {
              issue: "您饮酒主要种类?",
              value: "",
              remarks: "",
              items: [
                {
                  label: "烈性酒",
                  value: "0",
                },
                {
                  label: "啤酒",
                  value: "1",
                },
                {
                  label: "葡萄酒、米酒或黄酒",
                  value: "2",
                },
                {
                  label: "其他",
                  value: "4",
                },
                {
                  label: "不饮酒",
                  value: "5",
                },
              ],
            },
            {
              issue: "您的饮食习惯?",
              value: "",
              flavor: "",
              items: [
                {
                  label: "荤素均衡",
                  value: "0",
                },
                {
                  label: "啤酒",
                  value: "1",
                },
                {
                  label: "荤食为主",
                  value: "2",
                },
                {
                  label: "素食为主",
                  value: "3",
                },
              ],
              items1: [
                {
                  label: "多盐",
                  value: "4",
                },
                {
                  label: "多油",
                  value: "5",
                },
                {
                  label: "辛辣",
                  value: "6",
                },
                {
                  label: "清淡",
                  value: "7",
                },
              ],
            },
            {
              issue: "您每天夜间睡眠时长__小时，午睡__分钟",
              night: "",
              noon: "",
              sleepQuality: "",
            },
            {
              issue: "若您是女性，请问您是否绝经?",
              menopause: "",
              value: "",
            },
            {
              issue: "若您是女性，请问您是否有以下状况：",
              items: [
                {
                  title: "妊高症",
                  check: false,
                },
                {
                  title: "先兆子痫",
                  check: false,
                },
                {
                  title: "多囊卵巢综合症",
                  check: false,
                },
                {
                  title: "口服避孕药",
                  check: false,
                },
                {
                  title: "提前绝经",
                  check: false,
                },
                {
                  title: "无",
                  check: false,
                },
              ],
            },
          ],
        },
        {
          title: "运动情况调查",
          children: [
            {
              issue:
                "最近7天内，您有几天做了剧烈的体力活动，像提重物、挖掘、有氧活动、快速骑车等?",
              value: "",
              day: "",
              hour: "",
              minute: "",
            },
            {
              issue:
                "近7天内，您有几天做了适度的体力活动像提轻的物品以平常的速度骑车双人网球运动等?但不包括走路。",
              value: "",
              day: "",
              hour: "",
              minute: "",
            },
            {
              issue: "最近7天内，您有几天步行，且每一次至少10分钟?",
              value: "",
              day: "",
              hour: "",
              minute: "",
            },
            {
              issue:
                "最近 7天内，工作日您有多久是坐着的? 平均每天__小时__分钟.",
              hour: "",
              minute: "",
            },
          ],
        },
        {
          title: "心理及精神压力",
          children: [
            {
              issue: "您感觉工作/生活压力如何",
              value: "",
              items: [
                {
                  label: "很小",
                  value: "0",
                },
                {
                  label: "较小",
                  value: "1",
                },
                {
                  label: "适中",
                  value: "2",
                },
                {
                  label: "较大",
                  value: "3",
                },
                {
                  label: "很大",
                  value: "4",
                },
              ],
            },
            {
              issue: "您平时的心理状态如何",
              value: "",
              items: [
                {
                  label: "积极乐观",
                  value: "0",
                },
                {
                  label: "平静不易改变",
                  value: "1",
                },
                {
                  label: "非常容易受外界影响改变",
                  value: "2",
                },
                {
                  label: "消极低落",
                  value: "3",
                },
              ],
            },
            {
              issue: "我因一些小事为而烦恼",
              value: "",
              remarks: "",
            },
            {
              issue: "我在做事时很难集中精力",
              value: "",
              remarks: "",
            },
            {
              issue: "我感到情绪低落",
              value: "",
              remarks: "",
            },
            {
              issue: "我觉得做任何事都很费劲",
              value: "",
              remarks: "",
            },
            {
              issue: "我对未来充满希望",
              value: "",
              remarks: "",
            },
            {
              issue: "我感到害怕",
              value: "",
              remarks: "",
            },
            {
              issue: "我的睡眠不好",
              value: "",
              remarks: "",
            },
            {
              issue: "我很愉快",
              value: "",
              remarks: "",
            },
            {
              issue: "我感到孤独",
              value: "",
              remarks: "",
            },
            {
              issue: "我觉得我无法继续我的生活",
              value: "",
              remarks: "",
            },
          ],
        },
      ],
      form: null,
      page: "",
      isShowIdCard: true,
    };
  },
  methods: {
    getDateFromYearsAgoOrAfter(years) {
      const date = new Date();
      date.setFullYear(date.getFullYear() + years);
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },
    //出生日期
    dateConfirm(val) {
      const originalDate = new Date(val);
      const nextDay = new Date(originalDate);
      nextDay.setDate(nextDay.getDate() + 1); // 增加一天

      this.questionnaireData[0].children[2].value = nextDay
        .toISOString()
        .slice(0, 10);
      this.questionnaireData[0].children[2].popup.show = false;
    },
    //家庭住址
    familyConfirm(val) {
      const [province, city, district] = val;
      this.questionnaireData[0].children[6].provinceAndCity = `${province.name}-${city.name}-${district.name}`;
      this.questionnaireData[0].children[6].show = false;
    },
    //单位所在地
    companyConfirm(val) {
      const [province, city, district] = val;
      this.questionnaireData[0].children[7].provinceAndCity = `${province.name}-${city.name}-${district.name}`;
      this.questionnaireData[0].children[7].show = false;
    },
    //提交
    submit() {
      this.$refs.form
        .validate()
        .then(() => {
          let formData = JSON.parse(JSON.stringify(this.questionnaireData));

          let [sectionA, sectionB, sectionC, sectionD, sectionE] = formData;

          if (sectionB.children[2].items[14].remarks !== "") {
            sectionB.children[2].items[14].diseaseName =
              sectionB.children[2].items[14].remarks;
            delete sectionB.children[2].items[14].remarks;
          }

          delete sectionB.children[2].issue;
          let answer = {
            z0: sectionA.children[8].value,
            z1: sectionA.children[0].value,
            z2: sectionA.children[1].value,
            z3: sectionA.children[2].value,
            z4: sectionA.children[3].value,
            z5: sectionA.children[4].value,
            z6:
              sectionA.children[5].value === "其他"
                ? sectionA.children[5].remarks
                : sectionA.children[5].value,
            z7: `${sectionA.children[6].provinceAndCity}-${sectionA.children[6].address},居住时间${sectionA.children[6].durationOfResidence}年`,
            z8: `${sectionA.children[7].provinceAndCity}-${sectionA.children[7].address},居住时间${sectionA.children[7].durationOfResidence}年`,
            a1: sectionB.children[0].value,
            a2: sectionB.children[3].value,
            a3: sectionB.children[4].value,
            a4: {
              value: sectionB.children[1].value,
              bloodTransfusion: sectionB.children[1].bloodTransfusion,
              allergyL: sectionB.children[1].allergyL,
            },
            a5: { ...sectionB.children[2] },
            b1: sectionC.children[0].value,
            b2: {
              lengthAsASmoker: sectionC.children[1].lengthAsASmoker,
              quantity: sectionC.children[1].quantity,
            },
            b3: sectionC.children[2].value,
            b4:
              sectionC.children[3].value == "其他"
                ? sectionC.children[3].remarks
                : sectionC.children[3].value,
            b5: {
              value: sectionC.children[4].value,
              flavor: sectionC.children[4].flavor,
            },
            b6: {
              night: sectionC.children[5].night,
              noon: sectionC.children[5].noon,
              sleepQuality: sectionC.children[5].sleepQuality,
            },
            b7:
              sectionC.children[6].value === "是"
                ? `是，绝经年龄${sectionC.children[6].menopause}岁`
                : "否",
            b8: this.handleData(sectionC.children[7].items),
            c1:
              sectionD.children[0].value == "无相关体力活动"
                ? "无相关体力活动"
                : `${sectionD.children[0].value},每周${sectionD.children[0].day}天，每天${sectionD.children[0].hour}小时${sectionD.children[0].minute}分钟`,
            c2:
              sectionD.children[1].value == "无相关体力活动"
                ? "无相关体力活动"
                : `${sectionD.children[1].value},每周${sectionD.children[1].day}天，每天${sectionD.children[1].hour}小时${sectionD.children[1].minute}分钟`,
            c3:
              sectionD.children[2].value == "无相关体力活动"
                ? "无相关体力活动"
                : `${sectionD.children[2].value},每周${sectionD.children[2].day}天，每天${sectionD.children[2].hour}小时${sectionD.children[2].minute}分钟`,
            c4: {
              hour: sectionD.children[3].hour,
              minute: sectionD.children[3].minute,
            },
            d1: sectionE.children[0].value,
            d2: sectionE.children[1].value,
          };
          if (this.isShowIdCard) {
            answer.z9 = sectionA.children[9].value;
          }

          for (let i = 2; i <= 11; i++) {
            answer[`d${i + 1}`] =
              sectionE.children[i].value == "有"
                ? `有，每周${sectionE.children[i].remarks}天`
                : sectionE.children[i].value;
          }

          // 排序
          function moveItemToEnd(array, diseaseName) {
            let index = array.findIndex(
              (item) => item.diseaseName === diseaseName
            );
            if (index !== -1) {
              if (
                diseaseName ===
                "早发心脑血管病家族史（男性小于55岁，女性小于65岁）"
              ) {
                delete array[index].special;
              }
              let removedItems = array.splice(index, 1);
              array.push(removedItems[0]);
            }
          }

          // 要移动的项目列表
          const diseasesToMove = [
            "心房纤颤/心房扑动",
            "脑卒中史（或家族史）",
            "心肌梗死",
            "早发心脑血管病家族史（男性小于55岁，女性小于65岁）",
            "肾功能异常",
            "自身免疫性疾病",
            "视力下降或视物不清",
            "间歇性跛行",
          ];

          // 循环移动每一项
          diseasesToMove.forEach((diseaseName) =>
            moveItemToEnd(answer.a5.items, diseaseName)
          );

          //按键名排序
          let sortedKeys = Object.keys(answer).sort();
          let sortedObject = {};
          sortedKeys.forEach((key) => {
            sortedObject[key] = answer[key];
          });

          storage.session.set("question", JSON.stringify(sortedObject));
          if (this.page == "person") {
            this.$dialog.alert({
              message: "提交成功!",
            });
            this.$router.push({
              path: "/PersonOrders",
            });
          } else {
            this.SaveSyncQuestions(sortedObject);
          }
          return;
          // this.Toast("提交成功!");
        })
        .catch((err) => {
          console.error("submit", err);
          // this.Toast(`有问题未回答：${err[0]?.message}`);
          this.$dialog.alert({
            message: `有问题未回答：${err[0]?.message}`,
          });
          // showDialog({ title: "有问题未回答", message: err[0]?.message });
        });
    },
    addressProcessing(address) {
      const lastDashIndex = address.lastIndexOf("-");
      const commaIndex = address.indexOf(",");
      const yearIndex = address.indexOf("年");
      return {
        provinceAndCity: address.slice(0, lastDashIndex),
        address: address.slice(lastDashIndex + 1, commaIndex),
        durationOfResidence: address
          .slice(commaIndex + 1, yearIndex + 1)
          .match(/\d+/g)
          .map(Number),
      };
    },
    handleData(data) {
      let value = "";
      data.map((item) => {
        if (item.check) {
          value += item.title + ";";
        }
      });
      //去掉value最后的分号
      if (value.endsWith(";")) value = value.slice(0, -1);
      return value;
    },
    //数据回显
    dataEcho(res) {
      let temp = JSON.parse(JSON.stringify(this.questionnaireData));
      let [a, b, c, d, e] = temp;
      // 个人基本信息
      const personalInfoFields = ["z1", "z2", "z3", "z4", "z5"];
      personalInfoFields.forEach((field, index) => {
        a.children[index].value = res[field];
      });
      a.children[8].value = res.z0;
      if (this.isShowIdCard) {
        a.children[9].value = res.z9;
      }
      // 职业
      if (a.children[5].items.some((i) => i.label === res.z6)) {
        a.children[5].value = res.z6;
      } else {
        a.children[5].value = "其他";
        a.children[5].remarks = res.z6;
      }

      // 现家庭住址和工作单位所在地
      const addressFields = [
        "provinceAndCity",
        "address",
        "durationOfResidence",
      ];
      addressFields.forEach((field, index) => {
        a.children[6][field] = this.addressProcessing(res.z7)[field];
        a.children[7][field] = this.addressProcessing(res.z8)[field];
      });

      // 健康状况及家族史
      b.children[0].value = res.a1;
      b.children[1].value = res.a4.value;
      b.children[1].bloodTransfusion = res.a4.bloodTransfusion;
      b.children[1].allergyL = res.a4.allergyL;
      b.children[2].value = res.a5.value;
      b.children[3].value = res.a2;
      b.children[4].value = res.a3;
      let lastItem = res.a5.items.pop();
      res.a5.items.splice(4, 0, lastItem);

      lastItem = res.a5.items.pop();
      res.a5.items.splice(6, 0, lastItem);

      res.a5.items.forEach((item, index) => {
        Object.keys(item).forEach((key) => {
          if (key !== "diseaseName") {
            b.children[2].items[index][key] = item[key];
          }
        });
        if (item.diseaseName !== "其他疾病" && index === 14) {
          b.children[2].items[index].remarks = item.diseaseName;
          b.children[2].items[index].diseaseName = "其他疾病";
        }
      });

      // 生活方式信息
      const lifestyleFields = ["b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8"];
      lifestyleFields.forEach((field, index) => {
        if (field === "b2") {
          c.children[1].lengthAsASmoker = res.b2.lengthAsASmoker;
          c.children[1].quantity = res.b2.quantity;
        } else if (field === "b4") {
          if (c.children[3].items.some((i) => i.label === res.b4)) {
            c.children[3].value = res.b4;
          } else {
            c.children[3].value = "其他";
            c.children[3].remarks = res.b4;
          }
          // c.children[3].items[0]  = "其他";
          // c.children[3].items[0].remarks = res.b4;
        } else if (field === "b5") {
          c.children[4].value = res.b5.value;
          c.children[4].flavor = res.b5.flavor;
        } else if (field === "b6") {
          c.children[5].night = res.b6.night;
          c.children[5].noon = res.b6.noon;
          c.children[5].sleepQuality = res.b6.sleepQuality;
        } else if (field === "b7") {
          c.children[6].value = res.b7.includes("是") ? "是" : "否";
          c.children[6].menopause = res.b7.match(/\d+/g).map(Number);
        } else if (field === "b8") {
          c.children[7].items.forEach((item) => {
            if (res.b8.includes(item.title)) {
              item.check = true;
            }
          });
        } else {
          c.children[index].value = res[field];
        }
      });

      // 运动情况调查
      const exerciseFields = ["c1", "c2", "c3", "c4"];
      exerciseFields.forEach((field, index) => {
        if (field === "c4") {
          d.children[3].hour = res.c4.hour;
          d.children[3].minute = res.c4.minute;
        } else {
          if (res[field] == "无相关体力活动") {
            d.children[index].value = res[field];
          } else {
            let match = res[field].match(/每周(\d+)天，每天(\d+)小时(\d+)分钟/);
            // console.log("🚀 ~ exerciseFields.forEach ~ field:", res[field]);
            // console.log("🚀 ~ exerciseFields.forEach ~ match:", match);
            d.children[index].value = "有相关体力活动";
            d.children[index].day = match?.[1];
            d.children[index].hour = match?.[2];
            d.children[index].minute = match?.[3];
          }
        }
      });

      // 心理及精神压力
      const mentalHealthFields = [
        "d1",
        "d2",
        "d3",
        "d4",
        "d5",
        "d6",
        "d7",
        "d8",
        "d9",
        "d10",
        "d11",
        "d12",
      ];
      mentalHealthFields.forEach((field, index) => {
        if (res[field].includes("每周")) {
          e.children[index].value = "有";
          e.children[index].remarks = res[field].match(/\d+/g).map(Number);
        } else {
          e.children[index].value = res[field];
        }
      });

      this.questionnaireData = [a, b, c, d, e];
      this.questionnaireData[0].children[2].popup.date = new Date("2000/01/01");
      // console.log(
      //   "🚀 ~ dataEcho ~ this.questionnaireData:",
      //   this.questionnaireData
      // );
    },
    SaveSyncQuestions(answer) {
      let p = {
        idCard: answer.z9,
        remark: JSON.stringify(answer),
      };
      ajax
        .post(apiUrils.SaveInputQuestions, p, { nocrypt: true })
        .then((r) => {
          r = r.data;
          if (!r.success) {
            this.$dialog.alert({
              message: "提交失败!",
            });
            return;
          }
          Toast("提交成功！");
          this.$router.push({
            path: "/",
          });
        })
        .catch((err) => {
          Toast("系统繁忙。");
        });
    },
  },
  watch: {
    questionnaireData: {
      handler(newVal) {
        localStorage.setItem("questionnaireData", JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  created() {
    this.page = this.$route.query["page"];
    if (this.page) {
      this.isShowIdCard = false;
    }
    // 获取本地存储数据
    const savedData = localStorage.getItem("questionnaireData");
    if (savedData) {
      this.questionnaireData = JSON.parse(savedData);
      // console.log(this.questionnaireData);
    }

    // 数据回显测试
    try {
      this.dataEcho(res); // 注意这里的 res 应当是通过 props 或者其他方式传递给组件的
    } catch (error) {}
  },
  mounted() {
    this.questionnaireData[0].children[2].popup.date = new Date("2000/01/01");
  },
};
</script>

<style lang="scss" scoped>
/* 隐藏input */
// ::v-deep .el-input__wrapper {
//   box-shadow: none;
//   border: 0;
//   border-bottom: 1px solid #ccc;
//   border-radius: 0;
//   // box-shadow: 0 2px 2px -2px rgba(0, 0, 0, 0.5);
// }
html,
span,
p,
* {
  font-size: 16px;
}
.van-cell,
van-field__control {
  background-color: rgb(240, 240, 240);
}
.main {
  font-size: 16px !important;
}
:deep(.van-field__body) {
  border-bottom: 1px solid;
}
.hanlinAcademician {
  & > p {
    display: flex;
    .van-cell.van-field {
      padding: 0;
    }
  }
}
.input_b {
  border: none;
  border-bottom: 1px solid #000000;
  flex: 1;
  margin: 0 20px;
  box-sizing: border-box;
}
.van-radio {
  margin-bottom: 8px;
}
.main {
  font-family: fangsong;
  padding: 15px;

  header {
    & > p {
      padding: 0 15px;
    }
    & > p:nth-child(1) {
      text-align: center;
      font-size: 22px;
      font-weight: bold;
    }
    & > p:nth-child(2) {
      text-indent: 2em;
    }
    // text-align: center;
    // & > p:nth-child(1) {
    //   text-align: center;
    //   font-size: 18px;
    //   font-weight: bold;
    // }
    // & > p:nth-child(2) {
    //   text-indent: 2em;
    // }
    // & > div > div {
    //   span {
    //     min-width: 120px;
    //     align-content: center;
    //   }
    //   margin-bottom: 10px;
    //   display: flex;
    //   align-content: center;
    //   // justify-content: space-between;
    // }
  }
  .motion {
    .van-cell.van-field {
      padding: 0;
    }
  }
  .content {
    .content-item {
      & > p {
        font-size: 18px;
        font-weight: bold;
      }
      .content-choice {
        .operation-item {
          display: flex;
          flex-direction: column;
          .van-cell.van-field {
            padding: 0;
          }
          & > div {
            display: flex;
          }
        }
        .disease {
          padding: 8px;
          display: flex;
          flex-direction: column;
          .disease-item {
            margin-bottom: 8px;
            width: 100%;
            .disease-item-head {
              display: flex;
              span {
                display: flex;
                align-items: center;
              }
              .van-cell.van-field {
                padding: 0;
              }
            }
            .disease-item-body {
              margin-top: 5px;
              padding: 8px 15px 8px 15px;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
              border-radius: 8px;
              .van-cell.van-field {
                padding: 0 0 8px 0;
              }
              .medicalAdvice {
                display: flex;
                align-items: center;
                margin: 8px 0;
                .van-radio {
                  margin: 0;
                }
                & > span {
                  margin-right: 10px;
                }
              }
            }
          }
        }
        .other {
          display: flex;
          align-items: center;
          .van-cell.van-field {
            padding: 0;
          }
          & > * {
            margin-bottom: 8px;
          }
          & > span {
            margin-left: 15px;
          }
        }
        & > div {
          display: flex;
        }
      }
      .lineFeed {
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > p {
          margin-right: 15px;
        }
      }
      .title {
        text-align: center;
      }
    }
  }
}
</style>
