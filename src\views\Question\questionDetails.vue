<template>
    <div>
        <div id="details">
            <!-- 入职套餐 -->
            <div class="detailsDiv">
                <div class="detailsTop">
                    <div>
                        <div class="TopImg" v-if="dataList.sex == '1'">
                            <img src="../../assets/detailsMan.png" alt="">
                        </div>
                        <div class="TopImg" v-else-if="dataList.sex == '2'">
                            <img src="../../assets/detailsWoman.png" alt="">
                        </div>
                        <div class="TopImg" v-else>
                            <img src="../../assets/detailsSex01.png" alt="">
                        </div>
                    </div>
                    <div class="TopTitle">
                        <span>{{ dataList.clus_Name }}</span>
                    </div>
                    <div class="TopBtn">
                        <div>
                            <span>基础套餐</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- tab -->
            <div class="detailsDiv TabDiv">
                <div class="detailsTab">
                    <div class="TabBtn">
                        <div :class="BtnState == true ? 'BtnBlueL' : 'BtnWhiteL'" @click="btnIntroduction">
                            套餐简介
                        </div>
                        <div :class="btnKnowcs == true ? 'BtnBlueR' : 'BtnWhiteR'" @click='btnKnow'>
                            体检须知
                        </div>
                    </div>
                    <div class="TabText">
                        <div v-if="TabText" class="TabSpan">
                            <span>{{ PersonalSpan }}</span>
                        </div>
                        <div v-else>
                            <div class="NoticeDiv" v-for="(item, index) in NoticeDiv" :key="index">
                                <div class="NoticeNumber">
                                    <span>{{ index + 1 }}.</span>
                                </div>
                                <div class="NoticeText">
                                    <span>
                                        {{ item.NoticeText }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bottomDiv"></div>
                </div>
            </div>

            <!-- 套餐项目 -->
            <div class="SetMeal">
                <div class="detailsDiv SetMealBottom">
                    <div class="SetMealA">
                        <div class="SetMealImg">
                            <img src="../../assets/SetMeal.png" alt="套餐项目">
                        </div>
                        <span>套餐项目&nbsp;&nbsp;({{ detailed.length }}项)</span>
                    </div>
                </div>
                <div class="detailsDiv">
                    <div class="detailed" v-for="(items, indexS) in detailed" :key="indexS">
                        <div class="detailedTitle"><b>{{ indexS + 1 }}. {{ items.comb_Name }}</b></div>
                        <!-- <div class="detailedText" style="line-height: 0.5rem;color:red;">
                            <span>￥{{ items.comb_Price }}</span>
                        </div> -->
                        <div class="detailedText"><span>{{ items.note }}</span></div>
                        <!-- <div style="line-height:.42rem">
                        <div v-if="items.note_bs1!=null && items.note_bs1!=''"><b>适宜人群:</b>&nbsp;<span>{{items.note_bs1}}</span></div>
                        <div v-if="items.note_bs2!=null && items.note_bs1!=''"><b>不适人群:</b>&nbsp;<span>{{items.note_bs2}}</span></div>
                    </div>
                    <div class="detailedBottom"></div> -->
                    </div>
                </div>

            </div>

            <div class="footFixed">
                <!-- <div class="footLeft">
                <div class="LeftBtn">
                    <span>￥</span>
                    <span>{{dataList.price}}</span>
                </div>
            </div>
            <div class="footMiddle">
                <span>您需自费的项目有{{detailed.length}}个</span>
            </div>-->
                <div class="nextgoto" @click="ToCalendar">
                    <span>返回</span>
                </div>
            </div>
            <div class="footDiv"></div>
        </div>
    </div>
</template>
<script>
import { storage, ajax } from '../../common';
import apiUrls from '../../config/apiUrls';
export default {
    data() {
        return {
            dataList: [],
            PersonalSpan: '',
            img: '',
            btnKnowcs: true,
            // Tab按钮样式
            BtnState: false,
            // Tab页文本显示
            TabText: false,
            // 体检须知文本
            NoticeDiv: [{
                NoticeText: '提前预约，为了成功提交订单，您最晚要在体检前1天（具体以网站公示的号源情况为准）预订，请尽早预订。'
            }, {
                NoticeText: '如入职单位对体检项目有特殊要求，请于体检日在我中心前台进行具体咨询与调整。'
            }, {
                NoticeText: '以上所有解释权归本健康体检中心所有！'
            }],
            // 项目套餐
            detailed: [],
            // 套餐长度
            SetMealLength: 11,
            sex: '',
            type: "",
        }
    },
    created() {
        this.dataList = JSON.parse(storage.session.get('dataList'));
        // this.type=storage.session.get('type')
        this.sex = this.dataList.sex;
        this.PersonalSpan = this.dataList.PersonalSpan;
        this.GetItemCombList(this.dataList.clus_Code);
    },
    methods: {
        //获取套餐项目
        GetItemCombList(clus_Code) {
            var pData = {
                comb_code: clus_Code
            }
            ajax.post(apiUrls.GetItemCombList, pData, { nocrypt: true }).then(r => {
                var data = r.data.returnData;
                if (r.data.success) {
                    this.detailed = data;
                }
                else {
                    alert("暂无项目数据");
                }
            })
                .catch(e => {
                    alert("系统繁忙！请稍后再试");
                })
        },
        // 套餐简介
        btnIntroduction: function () {
            this.BtnState = true;
            this.btnKnowcs = false;
            this.TabText = true;
        },
        // 体检须知
        btnKnow: function () {
            this.btnKnowcs = true;
            this.BtnState = false;
            this.TabText = false;
        },

        ToCalendar() {
            this.$router.go(-1);
            // storage.session.set("GoAddItemType","P")
            // storage.session.set("combitem",JSON.stringify(this.detailed));
            // this.$router.push({
            //     path:'/addClusItem'
            // })
        }
    }
}
</script>
<style lang="scss" scoped>
.detailsDiv {
    width: 100%;
    background: white;
    box-sizing: border-box;
}

.detailsDiv .detailsTop {
    width: 92%;
    height: 1.24rem;
    margin: 0 auto;
    font-size: .28rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.detailsTop .TopImg {
    width: 1rem;
    height: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.detailsTop .TopTitle {
    width: calc(100% - 2.16rem);
    height: 100%;
    font-size: .36rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.TopTitle span {
    margin-left: .15rem;
}

.detailsTop .TopBtn {
    width: 1.4rem;
    height: 100%;
    color: #FFFFFF;
    letter-spacing: -0.0.04rem;
    display: flex;
    align-items: center;
}

.TopBtn div {
    width: 1.4rem;
    height: .48rem;
    background: #6A9BE4;
    border-radius: .04rem;
    text-align: center;
    line-height: .48rem;
}

/* Tab */
.TabDiv {
    border-top: .04rem solid #DFE3E9;
}

.detailsDiv .detailsTab {
    width: 92%;
    margin: 0 auto;
}

.detailsTab .TabBtn {
    width: 100%;
    height: .64rem;
    font-size: .28rem;
    display: flex;
    margin-top: .24rem;
}

.TabBtn .BtnBlueL {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #6A9BE4;
    color: white;
    border-radius: 2px 0 0 2px;
}

.TabBtn .BtnWhiteL {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
    color: #6A9BE4;
    border: 1px solid #6A9BE4;
    border-radius: 2px 0 0 2px;
}

.TabBtn .BtnBlueR {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #6A9BE4;
    color: white;
    border-radius: 0 2px 2px 0;

}

.TabBtn .BtnWhiteR {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
    color: #6A9BE4;
    border: 1px solid #6A9BE4;
    border-radius: 0 2px 2px 0;
}

.detailsTab .TabText {
    width: 100%;
    min-height: 1rem;
    font-size: .28rem;
    color: #646161;
    letter-spacing: -0.01px;
    line-height: .44rem;
    margin-top: .2rem;
}

.detailsTab .bottomDiv {
    width: 100%;
    height: .3rem;
}

.TabSpan {
    margin-top: .08rem;
}

.TabText .NoticeDiv {
    width: 100%;
    margin-top: .08rem;
    display: flex;
}

.NoticeDiv .NoticeNumber {
    width: .28rem;
}

.NoticeDiv .NoticeText {
    width: calc(100% - .28rem);
}

#details .SetMeal {
    width: 100%;
    font-size: .28rem;
    color: #4A4A4A;
    box-sizing: border-box;
    letter-spacing: -0.01px;
    margin-top: .16rem;
}

.SetMeal .SetMealBottom {
    border-bottom: 1px solid #DFE3E9;
}

.SetMeal .SetMealA {
    width: 92%;
    height: .84rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

.SetMealA .SetMealImg {
    width: .4rem;
    height: .4rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.SetMealImg img {
    margin-top: 3px;
}

.SetMealA span {
    margin-left: .1rem;
}

.SetMeal .detailed {
    width: 92%;
    margin: 0 auto;
    border-bottom: 1px solid #DFE3E9;
}

.detailed .detailedTitle {
    width: 100%;
    height: .8rem;
    display: flex;
    align-items: center;
}

.detailed .detailedText {
    color: #646161;
    letter-spacing: -0.01px;
    line-height: .44rem;
}

.detailed .detailedBottom {
    width: 100%;
    height: .24rem;
}

#details .footFixed {
    width: 100%;
    height: 1.16rem;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    font-size: .24rem;
    background: white;
    border-top: 1px solid #DFE3E9;
}

.footFixed .footLeft {
    width: 2.2rem;
    height: 96%;
    margin-left: 4%;
    color: #D0021B;
    letter-spacing: -0.02px;
    display: flex;
    align-items: center;
}

.footLeft .LeftBtn {
    width: 100%;
    height: .56rem;
    border-right: 2px solid #9B9B9B;
}

.LeftBtn span:nth-child(2) {
    font-size: .4rem;
}

.footFixed .footMiddle {
    width: 3.08rem;
    height: 100%;
    color: #9B9B9B;
    letter-spacing: -0.02px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.footFixed .footRight {
    width: 2.22rem;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #6A9BE4;
    font-size: .32rem;
    color: #FFFFFF;
    letter-spacing: -0.02px;
}

#details .footDiv {
    width: 100%;
    height: 1.16rem;
}

.nextgoto {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #6A9BE4;
    font-size: .32rem;
    color: #FFFFFF;
    letter-spacing: -0.02px;
}
</style>