<template>
  <div>
    <div id="PersonalOrder">
      <!-- top -->
      <div class="Information">
        <div class="InformationBox">
          <div class="InformationDiv">
            <div class="InformationTitle">
              <b>体检人信息</b>
            </div>
            <div class="InformationInput"></div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>证件号</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="请输入身份证号" v-model="InputCards" maxlength="18" readonly />
            </div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>姓名</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="请输入与身份证一致的姓名" v-model="InputNames" maxlength="6" readonly />
            </div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>手机号</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="请输入手机号" v-model="InputTels" maxlength="11" />
            </div>
          </div>
          <!-- <div v-if="IdCardType == 'F'">
            <div class="InformationDiv" @click="showTimeGat()">
              <div class="InformationTitle">
                <span>出生日期</span>
              </div>
              <div class="InformationInput">
                <input
                  type="text"
                  placeholder="点击选择日期"
                  v-model="birthDate"
                  maxlength="11"
                  readonly
                />
              </div>
            </div>
            <div class="InformationDiv">
              <div class="InformationTitle">
                <span>年龄</span>
              </div>
              <div class="InformationInput">
                <input type="text" v-model="gatAge" maxlength="11" readonly />
              </div>
            </div>
            <div class="InformationDiv">
              <div class="InformationTitle">
                <span>性别</span>
              </div>
              <div class="InformationInput">
                <div style="margin: auto">
                  <van-radio-group
                    v-model="Sex"
                    direction="horizontal"
                    @change="Sexconfim"
                  >
                    <van-radio name="1">男</van-radio>
                    <van-radio name="2">女</van-radio>
                  </van-radio-group>
                </div>
              </div>
            </div> 
            </div> -->
        </div>
      </div>

      <!-- middle -->
      <div class="time">
        <div class="timeDiv">
          <div class="timeTitle"><span>体检日期</span></div>
          <div class="timeChoice">
            <span>{{ calendarDay.date }}（{{ week }}）</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle"><span>报到时间</span></div>
          <div class="timeChoice">
            <span>{{ calendarDay.sumtimeName }}</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle"><span>体检类型</span></div>
          <div class="timeChoice">
            <span v-if="type == 'person'">个人体检</span>
            <span v-else-if="type == 'staff'">入职体检</span>
            <span v-else-if="type == 'vehicle'">驾驶证体检</span>
            <span v-else>问卷调查智能体检</span>
          </div>
        </div>
      </div>
      <div class="cluscs">
        <div class="timeDiv" v-if="price != 0">
          <div class="timeTitle"><span>基础套餐价格</span></div>
          <div class="timeChoice">
            <span class="priceChoice">￥</span><span>{{ price }}</span>
          </div>
        </div>
        <div class="timeDiv" v-if="item_price != 0">
          <div class="timeTitle"><span>自选项目价格</span></div>
          <div class="timeChoice">
            <span class="priceChoice">￥</span><span>{{ item_price }}</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle"><span>体检总价格</span></div>
          <div class="timeChoice">
            <span class="priceChoice">￥</span><span>{{ total_price }}</span>
          </div>
        </div>
        <!-- <div
          style="
            width: 92%;
            height: 0.8rem;
            margin: 0 auto;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            font-size: 0.28rem;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
          "
        >
          <div
            style="display: flex; justify-content: center; align-items: center"
          >
            <span style="font-size: 0.2rem; color: red"
              >温馨提示：微信预约暂未开放缴费功能,请于体检当天现场缴费。</span
            >
          </div>
        </div> -->
      </div>
      <!-- tab -->
      <div class="detailsDiv TabDiv">
        <div class="detailsTab">
          <div class="TabBtn">
            <div :class="BtnState == true ? 'BtnBlueL' : 'BtnWhiteL'" @click="btnIntroduction">
              套餐项目
            </div>
            <div :class="btnKnowcs == true ? 'BtnBlueR' : 'BtnWhiteR'" @click="btnKnow">
              自选项目
            </div>
          </div>
          <div class="SetMeal" v-if="TabText">
            <div v-if="combItem.length > 0">
              <div class="detailsDiv SetMealBottom">
                <div class="SetMealA">
                  <div class="SetMealImg">
                    <img src="../../assets/SetMeal.png" alt="套餐项目" />
                  </div>
                  <span>套餐项目&nbsp;&nbsp;({{ combItem.length }}项)</span>
                </div>
              </div>
              <div class="detailsDiv">
                <div class="detailed" v-for="(items, indexS) in combItem" :key="indexS">
                  <div class="detailedTitle">
                    <b>{{ indexS + 1 }}. {{ items.comb_Name }}</b>
                  </div>
                  <div class="detailedText">
                    <span>{{ items.note }}</span>
                  </div>
                  <div style="line-height: 0.42rem">
                    <div v-if="items.note_bs1 != null && items.note_bs1 != ''">
                      <b>适宜人群:</b>&nbsp;<span>{{ items.note_bs1 }}</span>
                    </div>
                    <div v-if="items.note_bs2 != null && items.note_bs1 != ''">
                      <b>不适人群:</b>&nbsp;<span>{{ items.note_bs2 }}</span>
                    </div>
                  </div>
                  <div class="detailedBottom"></div>
                </div>
              </div>
            </div>
            <div v-else>
              <div style="text-align: center; margin-top: 0.5rem">
                <img src="../../assets/kong.png" alt="" style="width: 2.3rem" />
              </div>
              <div style="
                  text-align: center;
                  line-height: 0.8rem;
                  letter-spacing: 0.03rem;
                ">
                暂无体检项目
              </div>
            </div>
          </div>
          <div class="SetMeal" v-else>
            <div v-if="addCombItem.length > 0">
              <div class="detailsDiv SetMealBottom">
                <div class="SetMealA">
                  <div class="SetMealImg">
                    <img src="../../assets/SetMeal.png" alt="套餐项目" />
                  </div>
                  <span>自选项目&nbsp;&nbsp;({{ addCombItem.length }}项)</span>
                </div>
              </div>
              <div class="detailsDiv">
                <div class="detailed" v-for="(Combitems, index) in addCombItem" :key="index">
                  <div class="detailedTitle">
                    <b>{{ index + 1 }}. {{ Combitems.comb_Name }}</b>
                  </div>
                  <div class="detailedText">
                    <span>{{ Combitems.note }}</span>
                  </div>
                  <div style="line-height: 0.42rem">
                    <div v-if="Combitems.note_bs1 != null && Combitems.note_bs1 != ''
                      ">
                      <b>适宜人群:</b>&nbsp;<span>{{
                        Combitems.note_bs1
                      }}</span>
                    </div>
                    <div v-if="Combitems.note_bs2 != null && Combitems.note_bs1 != ''
                        ">
                      <b>不适人群:</b>&nbsp;<span>{{
                        Combitems.note_bs2
                      }}</span>
                    </div>
                  </div>
                  <div class="detailedBottom"></div>
                </div>
              </div>
            </div>
            <div v-else>
              <div style="text-align: center; margin-top: 0.5rem">
                <img src="../../assets/kong.png" alt="" style="width: 2.3rem" />
              </div>
              <div style="
                  text-align: center;
                  line-height: 0.8rem;
                  letter-spacing: 0.03rem;
                ">
                暂无自选项目
              </div>
            </div>
            <!-- <div class="detailsDiv">
                <div class="detailed" v-for="(items,indexS) in combItem" :key="indexS">
                    <div class="detailedTitle"><span>{{indexS+1}}、{{items.comb_Name}}</span></div>
                    <div class="detailedText"><span>{{items.note}}</span></div>
                    <div class="detailedBottom"></div>
                </div>
            </div> -->
          </div>
          <div class="bottomDiv"></div>
        </div>
      </div>
      <!--  -->
      <!-- foot -->
      <div class="footFixed" v-if="onfimTrue == true">
        <div class="footRight" @click="PersonChoice">
          <span>立即预约</span>
        </div>
      </div>
      <div class="footFixed" v-else>
        <div class="footRightfalse">
          <span>立即预约</span>
        </div>
      </div>
      <div class="footDiv"></div>

      <!--遮罩层-->
      <van-overlay :show="show" v-show="show">
        <div class="vanoverBtn">
          <van-loading type="spinner" color="#1989fa">预约中...</van-loading>
        </div>
      </van-overlay>

      <div class="diaNow">
        <van-dialog v-model="ordershow" title="订单预约" show-cancel-button :beforeClose="chargeBtn" confirmButtonText="是"
          cancelButtonText="否">
          <div class="onfimItem" v-if="baseData.payFlag == 'T'">
            <span>订单支付须知!
              预约成功后如需取消订单，请前往体检中心进行退费操作，微信上暂不支持微信退费。请认真考虑是否要预约</span>
          </div>
          <div class="onfimItemTwo" v-else>
            <span>请确认是否预约！</span>
          </div>
        </van-dialog>
      </div>
      <!-- 遮罩层 -->
      <van-overlay :show="showORs" v-show="showORs">
        <div class="vanoverBtn">
          <van-loading type="spinner" color="#1989fa">获取订单数据...</van-loading>
        </div>
      </van-overlay>
      <van-popup v-model="showTime" position="bottom" :style="{ height: '35%' }" :close-on-click-overlay="true">
        <div>
          <van-datetime-picker v-model="currentDate" type="date" title="选择出生年月日" :min-date="minDate" :max-date="maxDate"
            @confirm="TimeChick" @cancel="cancelTime" />
        </div>
      </van-popup>
    </div>
    <!-- <div>
      <van-dialog v-model="XinguanTs" title="疫情防控通知" show-cancel-button :showCancelButton="false">
      <div style="width: 100%;"><div style="width: 90%;
    margin: auto;
    margin-top: .2rem;text-indent: .3rem;line-height: .44rem;">{{baseData.xinguantishi}}</div></div>
             
      </van-dialog></div> -->
  </div>
</template>
<script>
import { ajax, dataUtils, storage } from "../../common";
import Vue from "vue";
import apiUrls from "../../config/apiUrls";
import { Toast } from "vant";
export default {
  data() {
    return {
      baseData: Vue.prototype.baseData,
      calendarDay: [],
      XinguanTs: true,
      dataList: [],
      price: 0,
      item_price: 0,
      total_price: 0,
      //moenys:0,
      item_code: "",
      item_codes: "",
      item_name: "",
      CardText: "",
      img: "",
      PersonalSpan: "",
      InputNames: "",
      InputCards: "",
      InputTels: "",
      type: "",
      show: false,
      combItem: [],
      btnKnowcs: false,
      // Tab按钮样式
      BtnState: true,
      // Tab页文本显示
      TabText: true,
      addCombItem: [],
      itemType: "",
      questionInfo: {},
      ordershow: false,
      CardNo: "",
      clf_price: 0,
      clfshwo: false,
      onfimTrue: true,
      m: "",
      showORs: true,
      showTime: false,
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      currentDate: new Date(),
      birthDate: "",
      gatAge: "",
      radio: "",
      Sex: "",
      IdCardType: "", //身份证识别
      address: "",
    };
  },
  created() {
    //用户信息
    var user =
      JSON.parse(storage.session.get("tjinfos")) == null
        ? JSON.parse(storage.cookie.get("user"))
        : JSON.parse(storage.session.get("tjinfos"));
    //用户信息
    var infos = JSON.parse(storage.session.get("questionInfo"));
    this.questionInfo = infos == null ? this.questionInfo : infos;
    this.combItem = JSON.parse(storage.session.get("combitem"));
    //加项项目
    var aditem = JSON.parse(storage.session.get("Add_CombItem"));
    this.addCombItem = aditem == null ? this.addCombItem : aditem;
    this.type = storage.session.get("type");
    this.InputNames = infos.name;
    this.InputCards = infos.InputCards;
    this.InputTels = infos.tel;
    // this.address = user.address;
    //根据字段长度判断身份类别。十八位是身份证预约，否则港澳台预约
    if (user.idCard.length === 18) {
      this.IdCardType = "T";
    } else {
      this.IdCardType = "F";
    }
    this.CardNo = user.cardNo;
    var clusdata = JSON.parse(storage.session.get("dataList"));
    this.dataList = clusdata == null ? this.dataList : clusdata;
    //价格
    var moenys = storage.session.get("money");
    this.price = this.dataList.price;
    this.total_price = storage.session.get("money");
    this.item_price = (this.total_price - this.price).toFixed(2);
    this.calendarDay = JSON.parse(storage.session.get("calendarDay"));
    var weekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    this.week = weekDay[this.calendarDay.week];
    this.showORs = false;
    var item_code = "";
    for (var i = 0; i < this.combItem.length; i++) {
      item_code += this.combItem[i].comb_Code.trim() + ",";
    }
    for (var i = 0; i < this.addCombItem.length; i++) {
      item_code += this.addCombItem[i].comb_Code.trim() + ",";
    }
    this.item_codes = item_code;
  },
  methods: {
    Sexconfim(e) {
      this.Sex = e;
      // console.log(e);
    },
    cancelTime() {
      this.showTime = false;
    },
    //获取出生年月日。年龄
    TimeChick(value) {
      try {
        var Dates = new Date(value);
        //出生日期
        var birthFullYear = Dates.getFullYear();
        var birthMonth = Dates.getMonth();
        var birthDate = Dates.getDate();
        //现在时间 获取年龄
        var nowDate = new Date();
        var FullYears = nowDate.getFullYear();
        var Months = nowDate.getMonth();
        var DatesT = nowDate.getDate();
        if (FullYears > birthFullYear) {
          this.gatAge = FullYears - birthFullYear - 1;
          if (Months > birthMonth) {
            this.gatAge++;
          } else if (Months == birthMonth) {
            if (DatesT >= birthDate) {
              this.gatAge++;
            }
          }
          this.birthDate =
            Dates.getFullYear() +
            "-" +
            Dates.getMonth() +
            "-" +
            Dates.getDate();
          this.showTime = false;
        } else {
          Toast("您选择的年龄为0，请重新选择日期");
          return;
        }
      } catch (error) {
        console.log(error);
        Toast("获取失败！异常错误");
        return;
      }
    },
    showTimeGat() {
      this.showTime = true;
    },
    ShowInfoFlag() {
      Toast("暂不支持修改信息，如需更改请重新选择健康卡");
    },
    PersonChoice() {
      var that = this;
      that.ordershow = true;
    },
    //确认按钮确认
    chargeBtn(action, done) {
      var that = this;
      if (action === "confirm") {
        setTimeout(done, 1000);
        that.ToSuccess();
      } else {
        done();
      }
    },
    // 套餐简介
    btnIntroduction: function () {
      this.BtnState = true;
      this.btnKnowcs = false;
      this.TabText = true;
    },
    // 体检须知
    btnKnow: function () {
      this.btnKnowcs = true;
      this.BtnState = false;
      this.TabText = false;
    },
    ToSuccess() {
      if (this.InputNames == "") {
        Toast("请输入姓名");
        return;
      }
      if (this.InputCards == "") {
        Toast("请输入身份证件号码");
        return;
      }
      if (this.InputTels == "") {
        Toast("请输入手机号码");
        return;
      }
      if (this.IdCardType === "T") {
        if (dataUtils.isCardID(this.InputCards) != true) {
          Toast(dataUtils.isCardID(this.InputCards));
          return;
        }
        if (dataUtils.isTel(this.InputTels) != true) {
          Toast(dataUtils.isTel(this.InputTels));
          return;
        }
        // if (this.dataList.sex != 3 && this.itemType != "T") {
        //   this.Sex = parseInt(this.InputCards.substr(16, 1)) % 2;
        //   if (this.Sex == 1) {
        //     if (this.dataList.sex != 1) {
        //       Toast("请选择对应性别的套餐！");
        //       return;
        //     }
        //   }
        //   if (this.Sex == 0) {
        //     if (this.dataList.sex != 2) {
        //       Toast("请选择对应性别的套餐！");
        //       return;
        //     }
        //   }
        // }
      }
      // if (this.IdCardType === "F") {
      //   if (this.Sex == "") {
      //     Toast("请您选择性别！");
      //     return;
      //   }
      //   if (this.birthDate == "") {
      //     Toast("请您选择出生日期！");
      //     return;
      //   }
      // if (this.dataList.sex != 3 && this.itemType != "T") {
      //   if (this.Sex == 1) {
      //     if (this.dataList.sex != 1) {
      //       Toast("请选择对应性别的套餐！");
      //       return;
      //     }
      //   }
      //   if (this.Sex == 2) {
      //     if (this.dataList.sex != 2) {
      //       Toast("请选择对应性别的套餐！");
      //       return;
      //     }
      //   }
      // }
      // }
      // var item_price = 0;
      // var item_code = "";
      // var item_name = "";
      // for (var i = 0; i < this.addCombItem.length; i++) {
      //   item_price = dataUtils.NumberPrice(
      //     item_price,
      //     this.addCombItem[i].price
      //   );
      //   item_code += this.addCombItem[i].comb_Code + ",";
      //   item_name += this.addCombItem[i].comb_Name + ",";
      // }
      // if (this.itemType == "T") {
      //   this.dataList.clus_Code = "ZZZZZZ";
      // }
      var pData = {
        openid: storage.cookie.get("openid"),
        name: this.InputNames,
        idCard: this.InputCards,
        tel: this.InputTels,
        clus_Name: this.dataList.clus_Name,
        clus_Code: this.dataList.clus_code,
        begin_Time: this.calendarDay.date,
        type: this.type,
        sumtime_Code: this.calendarDay.sumtime_Code,
        sumtime_Name: this.calendarDay.sumtimeName,
        price: this.total_price, //总价
        clus_price: this.price, //基础套餐价格
        additem_price: this.item_price, //加项价格
        Combitem: JSON.stringify(this.combItem), //基础套餐数据
        addCombItem: JSON.stringify(this.addCombItem), //加项数据
        item_Code: this.item_codes,
        // item_Name: item_name,
        // item_type: this.itemType,
        // Json_Data: storage.session.get("result"),
        // marriage: this.questionInfo.radio,
        // stature: this.questionInfo.shengao,
        // weight: this.questionInfo.tiz,
        // blood_type: this.questionInfo.xueing,
        // bmi: this.questionInfo.zhishu,
        // abdominal: this.questionInfo.fuwei,
        // birthDate: this.birthDate,
        // IdCardType: this.IdCardType,
        // note: this.dataList.note,
        // sex: this.Sex,
        // age: this.gatAge,
        // m: this.m,
        // clf_price: this.clf_price,
      };
      this.show = true;
      console.log(pData);
      // debugger;
      ajax
        .post(apiUrls.PersonOrderAdd, pData, { nocrypt: true })
        .then((r) => {
          if (r.data.success) {
            storage.session.set(
              "OrderList",
              JSON.stringify(r.data.returnData.ord)
            );
            //更新cookie信息
            storage.cookie.set("user", JSON.stringify(r.data.returnData.uid));
            //Toast(r.data.returnMsg);
            if (r.data.returnData.payFlag == "T") {
              setTimeout(() => {
                this.$router.push({
                  path: "/PayOrders",
                });
                this.show = false;
              }, 1500);
            } else {
              setTimeout(() => {
                Toast(r.data.returnMsg);
                this.$router.replace({
                  path: "/MyOrderList",
                });
                this.show = false;
              }, 1500);
            }
          } else {
            this.show = false;
            Toast(r.data.returnMsg);
            return;
          }
        })
        .catch((e) => {
          alert("系统繁忙！请稍后再试");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
#PersonalOrder .Information {
  width: 100%;
  min-height: 3.48rem;
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.Information .InformationBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.InformationBox .InformationDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.InformationDiv .InformationTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.cluscs {
  width: 100%;
  // max-height: 2.67rem;
  background: white;
  margin-top: 0.16rem;
}

.cluscs .timeDiv {
  width: 92%;
  height: 0.67rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
}

.cluscs .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.cluscs .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.InformationDiv .InformationInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  margin-right: 0.1rem;
}

.InformationInput input {
  width: 100%;
  border: none;
  outline: medium;
  text-align: right;
  margin: auto;
}

#PersonalOrder .time {
  width: 100%;
  height: 2.6rem;
  background: white;
  margin-top: 0.16rem;
}

.time .timeDiv {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
}

.timeDiv .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.timeDiv .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.timeChoice .priceChoice {
  font-size: 0.24rem;
  color: #d0021b;
  letter-spacing: -0.02px;
}

.timeChoice span:nth-child(2) {
  font-size: 0.31rem;
  color: #d0021b;
  letter-spacing: -0.02px;
  line-height: 0.36rem;
}

/* 套餐 */
#PersonalOrder .SetMealNow {
  width: 100%;
  min-width: 2.66rem;
  background: white;
  margin-top: 0.16rem;
  // padding: 0.36rem 0;
}

.SetMealNow .NowBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.NowBox .SetTitle {
  width: 100%;
  height: 1rem;
  display: flex;
}

.SetTitle .SetImg {
  width: 0.75rem;
  height: 1rem;
  display: flex;
  // justify-content: start;
  align-items: center;
}

.SetImg img {
  margin-top: 3px;
}

.SetTitle .SetText {
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.NowBox .MealText {
  width: 100%;
  min-height: 1.3rem;
  max-height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  overflow-y: scroll;
}

/* 跳转按钮 */
.confirm {
  width: 92%;
  height: 0.96rem;
  background: #6a9be4;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancelfirm {
  width: 92%;
  height: 0.96rem;
  background: #b7b7b7;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 消息提示 */
.text-tip {
  display: block;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 15px 15px;
  line-height: 18px;
  position: fixed;
  left: 50%;
  bottom: 55%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
  border-radius: 3px;
  display: none;
  z-index: 9999;
  font-size: 14px;
  text-align: center;
}

.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.SetMeal {
  width: 100%;
  font-size: 0.28rem;
  color: #4a4a4a;
  box-sizing: border-box;
  letter-spacing: -0.01px;
  margin-top: 0.16rem;
  background: #ffffff;
  margin-bottom: 1.16rem;
}

.SetMealA .SetMealImg {
  width: 0.4rem;
  height: 0.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.SetMealImg img {
  margin-top: 3px;
}

.SetMealA span {
  margin-left: 0.1rem;
}

.SetMeal .SetMealBottom {
  border-bottom: 1px solid #dfe3e9;
}

.SetMeal .SetMealA {
  // width: 92%;
  height: 0.84rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
}

.SetMeal .detailed {
  width: 96%;
  margin: 0 auto;
  border-bottom: 1px solid #dfe3e9;
}

.detailed .detailedTitle {
  width: 100%;
  height: 0.8rem;
  display: flex;
  align-items: center;
}

.detailed .detailedText {
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
}

.detailed .detailedBottom {
  width: 100%;
  height: 0.24rem;
}

.footFixed {
  width: 100%;
  height: 1.16rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
  justify-content: center;
  align-items: center;
}

.footFixed .footLeft {
  width: 2.2rem;
  height: 96%;
  margin-left: 4%;
  color: #d0021b;
  letter-spacing: -0.02px;
  display: flex;
  align-items: center;
}

.footLeft .LeftBtn {
  width: 100%;
  height: 0.56rem;
  border-right: 2px solid #9b9b9b;
}

.LeftBtn span:nth-child(2) {
  font-size: 0.4rem;
}

.footFixed .footMiddle {
  width: 3.08rem;
  height: 100%;
  color: #9b9b9b;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footFixed .footRight {
  width: 94%;
  height: 84%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  border-radius: 5px;
}

.footFixed .footRightfalse {
  width: 94%;
  height: 84%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #878a8d;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  border-radius: 5px;
}

.addItem {
  background: white;
  margin-top: 0.2rem;
}

.combImg {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.combItemCs {
  width: 30%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #4a4a4a;
}

.noeItem {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

//tab
.detailsDiv {
  width: 100%;
  background: white;
  box-sizing: border-box;
}

.TabDiv {
  border-top: 0.04rem solid #dfe3e9;
}

.detailsDiv .detailsTab {
  width: 92%;
  margin: 0 auto;
}

.detailsTab .TabBtn {
  width: 100%;
  height: 0.8rem;
  font-size: 0.28rem;
  display: flex;
  margin-top: 0.24rem;
}

.TabBtn .BtnBlueL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnWhiteL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnBlueR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 0 2px 2px 0;
}

.TabBtn .BtnWhiteR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 0 2px 2px 0;
}

.detailsTab .TabText {
  width: 100%;
  min-height: 1rem;
  font-size: 0.28rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin-top: 0.2rem;
}

.TabSpan {
  margin-top: 0.08rem;
}

.onfimItem {
  width: 80%;
  height: 2.5rem;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: red;
  font-weight: bold;
  line-height: 0.44rem;
}

.onfimItemTwo {
  width: 80%;
  height: 1.5rem;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: red;
  font-weight: bold;
  line-height: 0.44rem;
}

.diaNow {
  .van-dialog {
    max-height: 55vh !important;
  }

  .van-dialog__header {
    height: 1rem !important;
    line-height: 1rem !important;
    padding-top: 0 !important;
  }

  .van-dialog__content {
    max-height: calc(55vh - 2rem) !important;
    overflow-y: scroll;
  }

  .van-dialog__footer {
    height: 1rem !important;
  }
}

// .Xinguan{
// .van-dialog__header{
//   color: #d0021b !important;
//   padding-top:14px  !important;
// }
// }
</style>
