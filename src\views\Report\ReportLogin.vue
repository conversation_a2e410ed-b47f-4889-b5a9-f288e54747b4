<template>
  <div>
    <div class="wrap">
      <div class="login">
        <div class="historyBtn">
          <van-button
            @click="fhindex"
            icon="wap-home"
            type="primary"
            size="small"
            color="linear-gradient(to right, rgb(121, 187, 255), rgb(84, 135, 212))"
          >
            返回首页
          </van-button>
        </div>
        <div class="hspLogo">
          <!-- <img src="../../assets/hspLogo.png" alt /> -->
          <img src="../../assets/icon/HomeLogo2.jpg" alt />
        </div>
        <div class="textinfo">
          <div style class="ReportLnc">
            <van-icon name="manager" size="32" color="#d1dde8" />
            <input
              type="text"
              placeholder="请输入姓名"
              maxlength="30"
              v-model="report_name"
            />
          </div>
          <div style class="ReportIdcard">
            <van-icon
              class="iconfont"
              class-prefix="icon"
              name="real_name"
              size="32"
              color="#d1dde8"
            />
            <input
              type="text"
              placeholder="请输入本人身份证号"
              maxlength="18"
              v-model="report_idcard"
            />
          </div>
          <div style class="ReportTel">
            <van-icon name="phone-circle" size="32" color="#d1dde8" />
            <!-- <van-icon name="show_gongsiguanli_fill" size="32" color="#d1dde8"/> -->
            <input
              type="tel"
              placeholder="请输入本人手机号码"
              maxlength="11"
              v-model="report_tel"
            />
          </div>
          <!-- <div style class="ReportTel">
            <van-icon name="comment" size="32" color="#d1dde8" />
            <input
              type="text"
              placeholder="请输入验证码"
              maxlength="7"
              v-model="report_code"
            />
            <van-button round type="info" :disabled="canYzm" @click="getYzm">{{
              yzmNum
            }}</van-button>
          </div> -->
          <div style="margin-top: 0.3rem">
            <van-button
              type="primary"
              size="large"
              color="linear-gradient(to right, #4bb0ff, #888bf2)"
              @click="btnLog"
              >登录</van-button
            >
          </div>
        </div>
        <div class="bottomtel">
          如有疑问请于上班时间致电
          <br />
          <a :href="'tel:' + baseData.hospitalTel3">{{
            baseData.hospitalTel3
          }}</a>
          <!-- | <a href="tel:02034858870">020-***********</a> -->
        </div>
      </div>
    </div>
    <!--遮罩层-->
    <van-overlay :show="show" v-show="show">
      <div class="vanoverBtn">
        <van-loading type="spinner" color="#1989fa">登录中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { storage, dataUtils, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Toast } from "vant";
import Vue from "vue";
export default {
  data() {
    return {
      baseData: Vue.prototype.baseData,
      show: false,
      // report_name:"陈某某",
      // report_idcard:"******************",
      // report_tel:"13377694479",
      report_name: "",
      report_idcard: "",
      report_tel: "",
      report_code: "",
      // Isyzm: false,
      canYzm: false,
      timeWait: 60,
      baseTime: 60,
      yzmNum: "获取验证码",
    };
  },
  created() {},
  methods: {
    //判断手机号码是否正确
    checkTel() {
      if (
        dataUtils.isTel(this.report_tel) == true &&
        this.report_idcard.length > 8 &&
        this.report_name.length >= 2 &&
        this.report_name.length < 30
      ) {
        let pData = {
          name: this.report_name,
          id_card: this.report_idcard,
          tel: this.report_tel,
        };
        ajax
          .post(apiUrls.GetPatientInfo, pData)
          .then((r) => {
            r = r.data;
            if (!r.success) {
              // toolsUtils.alert(r.returnMsg);
              this.$message({
                message: r.returnMsg,
                type: "success",
              });
              this.Isyzm = false;
              return;
            }
            if (r.returnData == "已存在验证码") {
              this.Isyzm = false;
            } else {
              this.Isyzm = true;
            }
          })
          .catch((e) => {
            this.$message({
              message: "网络异常",
              type: "warning",
            });
          });
      }
    },
    //获取验证码
    getYzm() {
      this.report_tel = this.report_tel.trim();
      if (
        this.report_tel == "" ||
        this.report_tel == null ||
        this.report_tel == undefined
      ) {
        Toast("手机号码不能为空！");
        return;
      }
      if (dataUtils.isTel(this.report_tel) != true) {
        Toast(dataUtils.isTel(this.report_tel));
        return;
      }
      this.canYzm = true;
      ajax
        .post(
          apiUrls.GetVerifyCode,
          { tel: this.report_tel },
          { nocrypt: true }
        )
        .then((r) => {
          r = r.data;
          if (!r.success) {
            Toast(r.returnMsg);
            this.canYzm = false;
            return;
          }
          Toast("验证码发送成功，5分钟内有效。");
          // toolsUtils.toast(r.returnData);
          this.yzmtime();
        });
    },
    //验证码定时
    yzmtime() {
      let _that = this;
      if (this.timeWait <= 1) {
        this.timeWait = this.baseTime;
        this.yzmNum = "获取验证码";
        this.canYzm = false;
        return;
      } else {
        this.timeWait--;
        this.yzmNum = this.timeWait + " s";
      }
      setTimeout(function () {
        _that.yzmtime();
      }, 1000);
    },
    btnLog() {
      if (
        this.report_name == "" ||
        this.report_tel == "" ||
        this.report_idcard == ""
      ) {
        Toast("请输入完整的信息");
        return;
      }

      if (dataUtils.isTel(this.report_tel) != true) {
        Toast(dataUtils.isTel(this.report_tel));
        that.show = false;
        return;
      }

      // 无短信验证
      var reportList = {
        idcard: this.report_idcard.trim(),
        name: this.report_name.trim(),
        tel: this.report_tel.trim(),
      };
      setTimeout(() => {
        storage.session.set("reportList", JSON.stringify(reportList));
        this.$router.push({
          path: "/ReportList",
        });
        that.show = false;
      }, 3000);
      return;

      //有短信验证
      if (dataUtils.isTel(this.report_tel) != true) {
        Toast(dataUtils.isTel(this.report_tel));
        return;
      }
      if ((this.report_code || "").length < 5) {
        Toast("验证码不正确");
        return;
      }

      this.show = true;

      let pData = {
        idcard: this.report_idcard.trim(),
        name: this.report_name.trim(),
        tel: this.report_tel.trim(),
        code: this.report_code.trim(),
      };

      ajax
        .post(apiUrls.ReportLogin, pData, { nocrypt: true })
        .then((r) => {
          if (r.data.success) {
            setTimeout(() => {
              storage.session.set("reportList", JSON.stringify(pData));
              this.$router.push({
                path: "/ReportList",
              });
              this.show = false;
            }, 2000);
          } else {
            this.show = false;
            Toast(r.data.returnMsg);
            return;
          }
        })
        .catch((e) => {
          this.show = false;
          Toast("网络异常");
        });
    },
    fhindex() {
      this.$router.push({
        path: "/",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.historyBtn {
  width: 100%;
  font-size: 0.36rem;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  background: #fdfdfd;
  box-shadow: 1px 1px 2px #fdfdfd;

  // .van-button--small {
  //   margin-left: 4%;
  // }
}

.van-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.login {
  position: absolute;
  width: 100%;
  height: 100%;
  background: url(../../assets/bottomBg.png) 0 bottom/100% no-repeat;
  background-color: #fff;
  padding-top: 0.75rem;
  box-sizing: border-box;
}

.login .hspLogo {
  text-align: center;
  margin: 0.8rem 0.22rem 0.05rem 0.22rem;
}

.login .hspLogo img {
  width: 2.4rem;
  height: 2.4rem;
  // display: inline;
}

.textinfo {
  width: 70%;
  height: 6rem;
  margin: 0 auto;
  padding-top: 0.3rem;
}

.textimg {
  display: flex;
  /* justify-content: center; */
  align-items: center;
  width: 20%;
  height: 1.1rem;
}

.textimg img {
  width: 59%;
  height: 0.6rem;
  padding-top: 0.23rem;
}

.ReportLnc {
  display: flex;
  width: 80%;
  height: 1rem;
  /* text-align: center; */
  /* margin: 0 auto; */
  justify-content: center;
  align-items: center;
  border-bottom: solid 1px #a9a0a0;
}

.ReportLnc input {
  width: 100%;
  height: 0.8rem;
  font-size: 14px;
  border: none;
  padding-top: 0.12rem;
  color: #999;
  -webkit-text-fill-color: #999;
}

.van-button--normal {
  width: 80%;
}

.bottomtel {
  width: 90%;
  font-size: 14px;
  // margin: 40px auto;
  margin: 28px auto 0 auto;
  text-align: center;
  line-height: 0.5rem;
  color: #939393;
}

.ReportLnc {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
}

.ReportLnc input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 0.3rem;
  color: #999;
  -webkit-text-fill-color: #999;
}

.ReportIdcard {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.ReportIdcard input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 0.3rem;
  color: #999;
  -webkit-text-fill-color: #999;
}

.ReportTel {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.ReportTel input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 0.3rem;
  color: #999;
  -webkit-text-fill-color: #999;
}

.van-button--large {
  border-radius: 0.32rem !important;
  font-size: 18px !important;
  letter-spacing: 11px;
}

//遮罩层
.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
