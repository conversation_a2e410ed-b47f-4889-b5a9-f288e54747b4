<template>
  <div>
    <div class="titletou">套餐加项（点击即可选择加项项目）</div>
    <div>
      <van-search
        v-model="value"
        show-action
        placeholder="请输入搜索关键词"
        @input="onSearch"
        @cancel="onCancel"
      >
      </van-search>
    </div>

    <div style="margin-top: 4px; font-size: 16px" />
    <div style="width: 100%; font-size: 16px; margin-bottom: 1.18rem">
      <div class="tree_select">
        <!-- 选择目录 -->
        <div class="panel tree_select_left">
          <div v-for="(item, index) in clusItems" :key="index">
            <div @click="activeIndex = index" class="left_items">
              <div>
                <div v-if="activeIndex === index" class="left_red">
                  <div style="margin: 15px 0 15px 8px">{{ item.text }}</div>
                </div>
                <div v-else class="left_item">{{ item.text }}</div>
              </div>
              <div class="left_item_top" v-if="item.badge && item.badge !== 0">
                {{ item.badge }}
              </div>
            </div>
          </div>
        </div>
        <!-- 选项 -->
        <div class="panel tree_select_right">
          <van-checkbox-group v-model="checkboxIds">
            <div
              v-for="(item2, index2) in clusItems[activeIndex].children"
              :key="index2"
            >
              <div
                style="
                  padding-top: 10px;
                  padding-bottom: 10px;
                  border-bottom: 1px solid #e5e5e5;
                "
              >
                <div class="right_item">
                  <div>
                    <van-checkbox
                      icon-size="25px"
                      :label="item2.text"
                      :name="item2.comb_Code"
                      @click="CombsHuchi(item2.comb_Code, item2.checkflag)"
                      :disabled="item2.checkflag"
                    >
                      {{ item2.text }}
                    </van-checkbox>
                  </div>
                  <div
                    style="color: red; padding-left: 2px"
                    v-if="!item2.isPrice"
                  >
                    ￥{{ item2.price }}
                  </div>
                  <!-- <view>{{item2.text}}</view> -->
                </div>
                <div class="right_note_price">
                  <div v-if="item2.Note">
                    <div class="right_note">
                      {{ item2.Note }}
                    </div>
                    <!-- <view ><view style="color: #606266;font-size: 14px;;text-indent: 2ch;">组合介绍</view> -->
                  </div>
                  <!-- <view style="color: red;">
										￥{{item2.price}}
									</view> -->
                </div>
                <div style=""></div>
              </div>
            </div>
          </van-checkbox-group>
        </div>
      </div>
    </div>

    <div class="footOne">
      <div class="footLeft">
        <div class="LeftBtn">
          <span>￥{{ price.toFixed(2) }}</span>
          <span></span>
        </div>
      </div>
      <div class="footMiddle">
        <span>已选{{ activeIds.length }}项体检项目</span>
      </div>
      <div class="footRight" @click="onCancel('T')">
        <span>立即预约</span>
      </div>
    </div>
    <div class="footDiv"></div>

    <van-popup
      v-model="showPop"
      round
      :style="{ 'max-height': '70%', width: '90%' }"
    >
      <div class="showBox">
        <div class="showTitles">确认项目</div>
        <!-- 统计 -->
        <div class="total">
          <div class="totalProject">
            <div class="totalText1">自选项目</div>
            <div class="totalText2">{{ confirmProj.sumNum }}个</div>
          </div>
          <div class="hr"></div>
          <div class="totalProject">
            <div class="totalText1">共计(包含材料费等)</div>
            <div class="totalText2">￥{{ total.toFixed(2) }}</div>
          </div>

          <!-- <div class="hr"></div>
          <div class="totalProject">
            <div class="totalText1">共计</div>
            <div class="totalText2">￥{{ price.toFixed(2) }}</div>
          </div> -->
        </div>
        <!-- 列表 -->
        <div
          class="showLists"
          v-for="(item, index) in confirmProj.clusProj"
          :key="index"
        >
          <div class="showTitle">{{ item.title }}</div>
          <div
            class="showText"
            v-for="(projObj, indexs) in item.projArr"
            :key="indexs"
          >
            <div class="showNo1">{{ indexs + 1 }}、{{ projObj.comb_Name }}</div>
            <div class="showNo2" v-if="!item.title.includes('套餐内项目')">
              {{ (projObj.price || 0.0).toFixed(2) }}
            </div>
          </div>
        </div>
      </div>
      <div class="toBox">
        <div class="toGo" @click="immediately">返回修改</div>
        <div class="toGo toGreen" @click="Confirmationsheet">确认预约</div>
      </div>
    </van-popup>

    <!--遮罩层-->
    <van-overlay :show="times" v-show="times">
      <div style="display: flex; justify-content: center; padding-top: 90%">
        <van-loading type="spinner" color="#1989fa"></van-loading>
      </div>
    </van-overlay>

    <van-overlay :show="show_material" v-show="show_material">
      <div style="display: flex; justify-content: center; padding-top: 90%">
        <van-loading type="spinner" color="#fdfdfd"
          >计算试管、材料费等...</van-loading
        >
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { ajax, storage, dataUtils } from "../common";
import apiUrils from "../config/apiUrls";
import { Toast } from "vant";
export default {
  data() {
    return {
      times: false,
      show_material: false,
      //确认项目弹框
      confirmProj: {
        sumNum: 0,
        sumPrice: 0,
        clusProj: [],
      },
      //初始数据后台返回
      items: [],
      imgs: "",
      showPop: false,
      clusItem: [
        {
          children: [],
        },
      ],
      price: 0,
      //已选套餐
      testItem: [],
      clus_price: 0, //套餐金额
      clusIn: {},
      value: "",
      itemAllB: [], //加项备份
      activeIds: [], //选中项目comb_Code
      checkboxIds: [], //选中项目comb_Code
      CombItemList: [], //选中项目
      activeIndex: 0,
      isPage: "", //是否套餐加项
      tc_combs: [], //加项项目
      total: 0, //总价（包含材料费）
      syncCombs: "", //用于同步的数据
    };
  },
  created() {
    this.isPage = this.$route.query["isAdd"];
    if (this.isPage == "T") {
      // let pr=0;
      this.testItem = JSON.parse(storage.session.get("clusIncludeItem")).map(
        (i) => {
          // pr=dataUtils.priceSum(pr,i.comb_Price);
          return {
            text: i.comb_Name.trim(),
            id: i.comb_Code.trim(),
            comb_Name: i.comb_Name.trim(),
            comb_Code: i.comb_Code.trim(),
            price: i.comb_Price,
            checkflag: true,
            isPrice: true,
          };
        }
      );
      // console.log("pr", JSON.parse(storage.session.get("dataList")).clus_Code);
      this.clusIn = {
        text: "套餐内项目",
        cls_name: "",
        cls_code: "",
        children: this.testItem,
        badge: this.testItem.length,
      };
      this.clus_price = JSON.parse(storage.session.get("dataList")).price;
    }

    this.AddClusItem();
  },
  computed: {
    //计算每个项目分类的徽标数
    clusItems() {
      this.price = 0;
      if (this.isPage == "T") {
        this.price = this.clus_price;
      }
      this.CombItemList = [];
      return this.clusItem.map((item) => {
        if (item.text == "套餐内项目") {
          return item;
        }
        var badgeNum = item.children.filter((i) => {
          if (this.checkboxIds.includes(i.id)) {
            this.CombItemList.push(i);
            this.price = dataUtils.priceSum(this.price, i.price);
            return i;
          }
        }).length;
        if (badgeNum > 0) {
          return { ...item, badge: badgeNum };
        }
        return item;
      });
    },
  },
  methods: {
    //检查是否互斥
    CombsHuchi(code, checkflag) {
      if (checkflag) {
        return;
      }
      let that = this;
      that.times = true;
      if (this.checkboxIds.includes(code)) {
        let p = {
          comb_code: code,
        };
        //获取所有分类项目数据
        ajax
          .post(apiUrils.CombsHuchi, p, { nocrypt: true })
          .then((r) => {
            r = r.data;
            if (!r.success) {
              that.checkboxIds = that.activeIds;
              Toast("添加失败！");
              that.times = false;
              return;
            }
            if (r.returnData.includes("无") || that.activeIds.length == 0) {
              that.activeIds = that.checkboxIds;
            } else {
              if (r.returnData.includes("comb_ext_code")) {
                let arr1 = JSON.parse(r.returnData)[0].comb_ext_code.split(";");
                let set1 = new Set(arr1);
                let set2 = new Set(that.activeIds);
                //获取交集,检查和已选的是否互斥
                let jiaoji = arr1.filter((item) => set2.has(item));
                if (jiaoji && jiaoji.length > 0) {
                  that.checkboxIds = that.activeIds;
                  let nameArry = [];
                  this.CombItemList.filter((x) => {
                    if (jiaoji.includes(x.id)) {
                      nameArry.push(x.text);
                    }
                  });
                  Toast("该项目与已选项目（" + nameArry.join(",") + "）互斥");
                } else {
                  that.activeIds = that.checkboxIds;
                }
              }
              // return;
            }
            that.times = false;
            return;
          })
          .catch((err) => {
            that.checkboxIds = that.activeIds;
            Toast("系统异常！");
            that.times = false;
          });
      } else {
        that.activeIds = that.checkboxIds;
        that.times = false;
      }
    },
    nextclick() {
      this.times = false;
      this.syncCombs = "";
      var sumNum = 0;
      var sumPrice = 0;
      var clusIn = []; //套餐内项目
      var clusOut = []; //套餐外项目

      this.tc_combs = []; //自选组合
      //套餐内项目
      if (this.isPage == "T") {
        clusIn = this.testItem.map((x) => {
          return {
            comb_Code: x.id,
            comb_Name: x.text,
            // price: x.price
          };
        });
      }
      // console.log("clusIn",clusIn);
      //自选加项
      let price_choose = 0;
      for (let i = 0; i < this.CombItemList.length; i++) {
        price_choose = dataUtils.priceSum(
          price_choose,
          this.CombItemList[i].price
        );
        clusOut.push({
          comb_Code: this.CombItemList[i].id,
          comb_Name: this.CombItemList[i].text,
          price: this.CombItemList[i].price,
        });
      }
      if (
        price_choose > dataUtils.priceSum(this.price, this.clus_price, "T") ||
        price_choose < dataUtils.priceSum(this.price, this.clus_price, "T")
      ) {
        Toast("金额异常");
        return;
      }
      //组合弹框数组
      this.confirmProj.sumNum = clusOut.length;
      this.confirmProj.sumPrice = this.price;

      this.confirmProj.clusProj = [];

      if (clusOut.length > 0) {
        this.confirmProj.clusProj.push({
          // title: "自选项目（" + clusOut.length + ")"+"￥"+ dataUtils.priceSum(this.price, this.clus_price, "T"),
          title:
            "自选项目（" +
            "￥" +
            dataUtils.priceSum(this.price, this.clus_price, "T") +
            ")",
          projArr: clusOut,
        });
        this.tc_combs = clusOut;
      }

      if (clusIn.length > 0) {
        this.confirmProj.clusProj.push({
          // title: "套餐内项目（" + clusIn.length + ")"+"￥"+this.clus_price,
          title: "套餐内项目（" + "￥" + this.clus_price + ")",
          projArr: clusIn,
        });
      }
      // if(this.tc_combs.length == 0){
      //   if (this.isPage == "T") {
      //     Toast("您未选择自选项目");
      //   }
      // }
      // this.showPop = true;

      if (this.tc_combs.length == 0) {
        this.total = this.price;
        if (this.isPage == "T") {
          Toast("您未选择自选项目");
          this.showPop = true;
        }
        this.show_material = false;
      } else {
        // this.show_material=true;
        //获取材料费等
        let clus_Code = "";
        if (this.isPage == "T") {
          clus_Code = JSON.parse(storage.session.get("dataList")).clus_Code;
        }

        let pData = {
          clus_Code: clus_Code,
          choose_comb_code: clusOut.map((x) => x.comb_Code.trim()).join(","),
        };
        // console.log(pData);return
        ajax
          .post(apiUrils.CombsMaterial, pData, { nocrypt: true })
          .then((r) => {
            let data = JSON.parse(r.data.returnData)[0];
            // console.log("data",data);
            if (data.error == "OK") {
              this.total = data.total;
              this.syncCombs = data.add_comb_code;
              this.showPop = true;
            } else {
              Toast(data.error);
              // return;
            }
            this.show_material = false;
          })
          .catch((e) => {
            alert("系统繁忙！请稍后再试");
          });
      }
    },
    immediately() {
      this.showPop = false;
    },
    Confirmationsheet() {
      //加项为零，判断是否自选加项，是则不能为零
      if (this.tc_combs.length === 0) {
        if (this.isPage == "T") {
          // Toast("您未选择自选项目");
        } else {
          Toast("加项不能为空！");
          return;
        }
      } else {
        storage.session.set(
          "chooseItem",
          JSON.stringify({
            totalPrice: this.total,
            chooseCombCode: this.tc_combs,
            add_comb_code: this.syncCombs,
          })
        );
      }

      if (this.isPage != "T") {
        let dataList = {
          clus_Code: "",
          sex: 3,
          clus_Name: "自选项目",
          price: this.total,
          PersonalSpan: "",
        };
        window.sessionStorage.setItem("dataList", JSON.stringify(dataList));
      }
      this.showPop = false;
      this.$router.push({
        path: "/PersonBookSum",
      });
    },

    AddClusItem() {
      let that = this;
      that.items = [];
      that.checkboxIds = [];
      that.activeIds = [];
      let p = {
        kw: "GetAddClusItemList001",
      };
      if (this.isPage == "T") {
        that.items.push(this.clusIn);
        that.testItem.filter((x) => {
          that.checkboxIds.push(x.id);
        });
        that.activeIds = that.checkboxIds;
        // console.log("99", this.clusIn);
        p.clus_Code = JSON.parse(storage.session.get("dataList")).clus_Code;
        p.sex = JSON.parse(storage.session.get("dataList")).sex;
      } else {
        p.clus_Code = "";
      }

      //获取所有分类项目数据
      ajax
        .post(apiUrils.GetAddClusItemList, p, { nocrypt: true })
        .then((r) => {
          r = r.data;
          if (!r.success) {
            Toast("加载数据失败！");
            return;
          }

          let addCombs = JSON.parse(r.returnData);
          //  console.log("addCombs",addCombs);
          //有套餐的加项，则去掉套餐组合
          if (this.isPage == "T") {
            for (let i = 0; i < addCombs.length; i++) {
              let children = addCombs[i].children.filter((y) => {
                let bool_add = true;
                for (let j = 0; j < this.clusIn.children.length; j++) {
                  if (this.clusIn.children[j].id.trim() == y.id.trim()) {
                    bool_add = false;
                    break;
                  }
                }
                if (bool_add) {
                  return {
                    ...y,
                    id: y.id.trim(),
                    comb_Code: y.id.trim(),
                    comb_Name: y.text.trim(),
                  };
                }
              });
              if (children && children.length > 0) {
                let ite = {
                  text: addCombs[i].text,
                  cls_name: addCombs[i].cls_name,
                  cls_code: addCombs[i].cls_code.trim(),
                  children: children,
                };
                that.items.push(ite);
              }
            }
          } else {
            addCombs.filter((x) => {
              let ite = {
                text: x.text,
                cls_name: x.cls_name,
                cls_code: x.cls_code.trim(),
                children: x.children.map((y) => {
                  return {
                    ...y,
                    id: y.id.trim(),
                    comb_Code: y.id.trim(),
                    comb_Name: y.text.trim(),
                  };
                }),
              };
              that.items.push(ite);
            });
          }

          that.clusItem = that.items;
          that.itemAllB = that.items;
          // console.log(that.items);
          this.times = false;
        })
        .catch((err) => {
          Toast("系统异常！");
        });
    },
    onSearch() {
      let that = this;
      let target = []; //目标
      // this.active = 0;
      if (that.value.length === 0) {
        that.clusItem = that.itemAllB;
        return;
      }
      // let itemAllB=that.itemAllB
      //防止that.itemAllB丢失children
      let itemAllB = JSON.parse(JSON.stringify(that.itemAllB));
      // console.log(itemAllB);
      that.clusItem = [];
      //搜索目标
      for (let i = 0; i < itemAllB.length; i++) {
        let items = itemAllB[i].children;
        let itemChi = [];
        for (let j = 0; j < items.length; j++) {
          //迷糊搜索comb_name
          let a = items[j].text.includes(that.value);
          if (a) {
            itemChi.push(items[j]);
          }
        }
        if (itemChi != [] && itemChi.length > 0) {
          let _item = itemAllB[i];
          _item.children = itemChi;
          target.push(_item);
        }
      }
      that.clusItem = target;
      this.activeIndex = 0;
      this.$forceUpdate(); //更新渲染
    },
    onCancel(type) {
      let that = this;
      that.clusItem = that.itemAllB;
      if (type == "T") {
        this.value = "";
        this.show_material = true;
        setTimeout(() => {
          this.nextclick();
        }, 2000);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.titletou {
  width: 100%;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6a9be4;
  font-size: 16px;
  color: #ffffff;
}

.tree_select {
  // width: 100%;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  // padding-right: 3%;
  height: 75vh;
}

.panel {
  width: 100%;
  overflow-y: auto;
  /* 允许内容垂直滚动 */
}

.tree_select_left {
  width: 33%;
}

.left_items {
  display: flex;
}

.left_red {
  border-left: 2px solid red;
}

.left_item {
  margin: 15px 0 15px 13px;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.left_item_top {
  border-radius: 50% 50% 50% 50%;
  background-color: red;
  color: white;
  font-size: 10px;
  width: 15px;
  height: 15px;
  display: flex;
  justify-content: center;
  align-content: center;
}

.tree_select_right {
  width: 65%;
}

.right_item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 10px 5px 0 5px;
}

.right_note_price {
  margin: 3px auto 7px 6px;
}

.right_note {
  color: #606266;
  font-size: 14px;
  text-indent: 28px;
}

.footOne {
  width: 100%;
  height: 1.16rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
}

.footOne .footLeft {
  width: 2.2rem;
  height: 96%;
  margin-left: 4%;
  color: #d0021b;
  letter-spacing: -0.02px;
  display: flex;
  align-items: center;
}

.footOne .footLeft .LeftBtn {
  width: 100%;
  height: 0.56rem;
  border-right: 2px solid #9b9b9b;
}

.footOne .footLeft .LeftBtn span:nth-child(2) {
  font-size: 0.4rem;
}

.footOne .footMiddle {
  width: 3.08rem;
  height: 100%;
  color: #9b9b9b;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footOne .footRight {
  width: 2.22rem;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
}

@mixin flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.showBox {
  width: 95%;
  max-height: 60vh;
  margin-left: 5%;
  overflow-y: auto;
  color: #4a4a4a;

  .showTitles {
    width: 100%;
    height: 0.8rem;
    @include flex();
    font-size: 0.36rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
  }

  // 统计
  .total {
    width: 100%;
    height: 1.5rem;
    margin-top: 0.4rem;
    box-shadow: 3px 3px 7px #888888;
    border-radius: 5px;
    padding-bottom: 0.2rem;
    @include flex();
    font-size: 0.32rem;

    .totalProject {
      width: calc(50% - 0.5px);
      height: 100%;

      .totalText1 {
        width: 100%;
        height: 50%;
        @include flex();
        align-items: flex-end;
      }

      .totalText2 {
        width: 100%;
        height: 50%;
        @include flex();
        color: red;
      }
    }

    .hr {
      height: 70%;
      width: 1px;
      background: #dcdfe6;
    }
  }

  // 列表
  .showLists {
    width: 100%;
    font-size: 0.28rem;
    color: #4a4a4a !important;
    padding: 5px 0;
    box-shadow: 3px 3px 7px #888888;
    margin-top: 0.2rem;
    border-radius: 5px;
    @include flex();
    flex-direction: column;

    .showTitle {
      width: 100%;
      height: 0.8rem;
      @include flex();
      border-bottom: 1px solid #e5e5e5;
      font-weight: 600;
      font-size: 0.36rem;
    }

    .showText {
      width: 100%;
      display: flex;
      margin-top: 0.1rem;
      margin-bottom: 0.1rem;

      .showNo1 {
        width: 80%;
        min-height: 0.6rem;
        @include flex();
        justify-content: flex-start;
        padding-left: 0.2rem;
      }

      .showNo2 {
        width: 20%;
        min-height: 0.6rem;
        @include flex();
        justify-content: flex-start;
        padding-left: 0.2rem;
        color: red;
      }
    }
  }
}

.toBox {
  width: 100%;
  height: 1.4rem;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .toGo {
    width: 40%;
    height: 0.8rem;
    color: #4a4a4a;
    border-radius: 5px;
    @include flex();
    font-size: 0.36rem;
    background: white;
    border: 1px solid #e5e5e5;
    letter-spacing: 1px;
  }

  .toGreen {
    background: #409eff !important;
    color: white;
  }
  .vanoverBtnC {
    display: flex;
    padding-top: 90%;
    justify-content: center;
  }
}
</style>
