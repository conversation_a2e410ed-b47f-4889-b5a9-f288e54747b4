<template>
  <div class="pdf">
    <div class="pdf-tab">
      <div
        :class="['btn-def',{'btn-active':activeIndex==index}]"
        v-for="(item,index) in pdfList"
        @click.stop="pdfClick(item.pdfUrl,index)"
      >{{item.title}}</div>
    </div>
    <van-slider
      style=" width: 88%; margin-left: 6%;"
      v-model="scale"
      :min="100"
      :max="200"
      bar-height="4px"
      active-color="#ee0a24"
      inactive-color="#657c94"
    >
      <template #button>
        <div class="custom-button">{{ scale }}</div>
      </template>
    </van-slider>
    <br />
    <pdf :style="{ width: scale + '%'}" v-for="i in numPages" :key="i" :src="pdfUrl" :page="i"></pdf>
  </div>
</template>
<script>

import { toolsUtils } from '../../common'
import pdf from 'vue-pdf'
export default {
  components: {
    pdf
  },
  data() {
    return {
      pdfList: [
        {
          pdfUrl: "https://dakaname.oss-cn-hangzhou.aliyuncs.com/file/2018-12-29/1546049718768.pdf",
          title: "你好，2019年"
        },
        {
          pdfUrl: "http://file.gp58.com/file/2018-11-14/111405.pdf",
          title: "中信证券观点"
        },
        {
          pdfUrl: "https://dakaname.oss-cn-hangzhou.aliyuncs.com/file/2018-12-28/1546003237411.pdf",
          title: "12月投资月刊"
        },
        {
          pdfUrl: "https://dakaname.oss-cn-hangzhou.aliyuncs.com/file/2018-12-28/1546003282521.pdf",
          title: "丰岭资本观点"
        },
      ],
      pdfUrl: '',
      numPages: 1,
      activeIndex: 0,
      scale: 100,
    }

  },
  created() {

  },
  mounted: function () {
    this.pdfTask(this.pdfList[0].pdfUrl)
  },
  methods: {
    pdfTask(pdfUrl) {
      var self = this
      var loadingTask = pdf.createLoadingTask(pdfUrl)
      loadingTask.promise.then(pdf => {
        self.pdfUrl = loadingTask
        self.numPages = pdf.numPages
      }).catch((err) => {
        console.error('pdf加载失败')
      })
    },
    pdfClick(pdfUrl, index) {
      if (index == this.activeIndex) return
      this.activeIndex = index
      this.pdfUrl = null
      this.scale = 100;
      this.pdfTask(pdfUrl)
    },
    //放大
    scaleD() {
      this.scale += 15;
    },

    //缩小
    scaleX() {
      if (this.scale == 100) {
        return;
      }
      this.scale += -15;
    },
  }
}
</script>
<style lang="scss" scoped>
.van-icon-checked:before {
  color: red;
}
.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.custom-button {
  width: 26px;
  color: #fff;
  font-size: 10px;
  line-height: 18px;
  text-align: center;
  background-color: #ee0a24;
  border-radius: 100px;
}
</style>