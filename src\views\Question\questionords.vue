<template>
  <div>
    <div id="orderConfirm">
      <div class="packageList">
        <section>
          <h3 class="clearfix listTitle">
          </h3>
          <div class="headerss">
            <img src="../../assets/sdadw.png" alt />
            <div class="toggle">
              <span>
                为了您的健康，我们为您推荐以下健康体检项目，
                <br />如有疑问请联系我们。祝您身体健康！
              </span>
            </div>
          </div>
        </section>
        <section class="obvious">
          <!-- <van-checkbox v-model="checked" @change="dada(checked)">复选框</van-checkbox> -->
          <div class="listInfor">
            <div>
              <h4 class="clearfix" style="border-bottom:1px solid #018bf0">
                <span class="fl">基础体检套餐</span>
                <!-- <span class="fr">
                  <i>{{clusInfo.length}}</i>项
                </span> -->
              </h4>
              <ul>
                <li>
                  <div class="detailed" style="display: flex;height: 1.5rem;">
                    <div class="detaDiy">
                      <img src="../../assets/detailsMan.png" alt="头像" v-if="info.sex == '1'" />
                      <img src="../../assets/detailsWoman.png" alt="头像" v-else-if="info.sex == '2'" />
                      <img src="../../assets/detailsSex.png" alt="头像" v-else />
                    </div>
                    <div class="detaDiyTwe">
                      <span>{{ clusInfo[0].clus_name }}</span>
                    </div>
                    <div class="detaDiyThree" @click="nextgoto">
                      <span class="detaDiyFive">点击查看</span>
                    </div>
                  </div>
                  <div>
                    <div class="detaDiyFiert">{{ clusInfo[0].clus_note }}</div>
                    <div class="detaDiySei">￥ {{ clusInfo[0].clus_price }}</div>
                  </div>
                </li>
              </ul>
            </div>
            <div style="margin-top: 0.2rem;">
              <!-- <div></div> -->
              <h4 class="clearfix" style="border-bottom:1px solid #018bf0">
                <span class="fl">我们为您推荐以下体检项目</span>
                <span class="fr">
                  <i>共 {{ QuestionItemData.length }}</i>项
                </span>
              </h4>
              <!-- <div class="clearfix">
                <span class="fl">我们为您推荐以下体检项目</span>
                <span class="fr">
                  <i>共 {{ QuestionItemData.length }}</i>项
                </span>
              </div> -->
              <div>
                <div class="vancell">
                  <van-checkbox-group v-model="Additemdata" @change="changeGroup">
                    <div class="selTOne" v-for="(item, index) in DaseList" :key="index">
                      <div class="noeDate">
                        <!-- <van-checkbox v-model="items.checkflag" :key="indexs" @click="dd(items,items.comb_code,index,item.type,items.checkflag)"><b>{{items.comb_name}}</b> </van-checkbox> -->
                        <van-checkbox :name="item"><b>{{ item.comb_name }}</b> </van-checkbox>
                        <span></span>
                      </div>
                      <div class="selTWe">
                        <div class="selTWes">
                          <span>{{ item.note }}</span>
                        </div>
                        <div>
                          <!-- <div style="line-height: 0.5rem;color: #018bf0;" v-if="items.note_bs1 != null">
                            <b>适宜人群:</b>&nbsp;
                            <span>{{ items.note_bs1 }}</span>
                          </div>
                          <div style="line-height: 0.5rem;color: red;" v-if="items.note_bs2 != null">
                            <b>不适人群:</b>&nbsp;
                            <span>{{ items.note_bs2 }}</span>
                          </div> -->
                          <div style="line-height: 0.5rem;color:red;">
                            <b>￥</b>&nbsp;
                            <b>{{ item.price }}</b>
                          </div>
                        </div>
                      </div>
                    </div>
                  </van-checkbox-group>
                </div>
              </div>
              <div v-if="DaseList.length <= 0">
                <div style="width: 100%;height: 100%;">
                  <img src="../../assets/nullData.png" alt="暂无数据" style="width: 100%;">
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <div class="clearfix affirmBox">
        <h5>
          共加
          <span>{{ Additemdata.length }}</span>个项目
        </h5>
        <p style="color:red">￥ {{ price }}</p>
        <span class="addcomb" @click="next()" v-if="methodsFlag == true">立即预约</span>
        <!-- <span class="coicksum" @click="next('comb')" v-if="additemShow==true && methodsFlag==true">加项</span> -->
      </div>



      <!-- <van-popup v-model="showPop" round :style="{ 'max-height': '70%', width: '90%' }">
        <div style="font-size: 0.36rem;margin-left: 5%;margin: 0.5rem;">
          <div style="margin: 0.1rem;">
          套餐：{{ clusInfo[0].clus_name }}
          </div>
          <div style="margin: 0.1rem;">
            自选项目：{{ Additemdata.length }}项
          </div>
          <div style="margin:0.2rem 0.1rem;">
            总价：
            <span style=" color: red;">￥{{ total.toFixed(2) }}</span>(包含材料费)
          </div>
        </div>
      <div class="toBox">
        <div class="toGo" @click="showPop = false">返回修改</div>
        <div class="toGo toGreen" @click="next">确认预约</div>
      </div>
    </van-popup> -->

      <van-popup v-model="showPop" round :style="{ 'max-height': '70%', width: '90%' }">
        <div class="showBox">
          <div class="showTitles">确认项目</div>
          <!-- 统计 -->
          <div class="total">
            <div class="totalProject">
              <div class="totalText1">自选项目</div>
              <div class="totalText2">{{ Additemdata.length }}个</div>
            </div>
            <div class="hr"></div>
            <div class="totalProject">
              <div class="totalText1">共计(包含材料费等)</div>
              <div class="totalText2">￥{{ total.toFixed(2) }}</div>
            </div>
          </div>
          <!-- 列表 -->
          <div class="showLists" v-for="(item, index) in confirmProj" :key="index">
            <div class="showTitle">{{ item.clus_name }}
              <span>（￥{{ item.price }}）</span>
            </div>
            <div class="showText" v-for="(projObj, indexs) in item.combs" :key="indexs">
              <div class="showNo1">{{ indexs + 1 }}、{{ projObj.comb_Name }}</div>
              <div class="showNo2" v-if="item.clus_name.includes('自选项目')">{{ (projObj.price || 0.00).toFixed(2) }}</div>
            </div>
          </div>
        </div>
        <div class="toBox">
          <div class="toGo" @click="showPop = false">返回修改</div>
          <div class="toGo toGreen" @click="Confirmationsheet">确认预约</div>
        </div>
      </van-popup>


    </div>
    <!--遮罩层-->
    <van-overlay :show="show" v-show="show">
      <div class="vanoverBtn">
        <van-loading type="spinner" color="#1989fa">获取中...</van-loading>
      </div>
    </van-overlay>
    <van-overlay :show="show_material" v-show="show_material">
      <div style="display: flex;justify-content: center;padding-top: 90%;">
        <van-loading type="spinner" color="#fdfdfd">计算试管、材料费等...</van-loading>
      </div>
    </van-overlay>


    <!-- <div>
      <van-dialog v-model="XinguanTs" title="体检通知提示" show-cancel-button :showCancelButton="false">
      <div style="width: 100%;"><div style="width: 90%;
    margin: auto;
    margin-top: .2rem;text-indent: .3rem;line-height: .44rem;">{{baseData.tongz}}</div></div>
             
      </van-dialog></div> -->
  </div>
</template>
<script>
import Vue from 'vue'
import { Toast } from 'vant';
import { ajax, storage, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      info: [],
      XinguanTs: true,
      clusInfo: [],
      show: false,
      show_material: false,
      showPop: false,
      total: 0,//总价（包含材料费）
      confirmProj: [],
      addItem: [],
      QuestionItemData: [],
      questionAddItem: "",
      combitem: [],//套餐项目
      additemShow: false,
      DaseList: [],
      checked: false,
      flag: "T",
      radio: "",
      radio1: "",
      radio2: "",
      activeName: '',
      itemData: [],//数据整合                   
      methodsFlag: true,

      price: 0,
      clus_price: 0,
      Additemdata: [],
      syncCombs: "",//加项材料费等
      dataList:{},
      combs:[],

    };
  },
  created() {
    try {
      this.info = JSON.parse(storage.session.get("questionInfo"));
      this.clusInfo = JSON.parse(storage.session.get("questionClus"));
      console.log("this.clusInfo", this.clusInfo);
      if (this.clusInfo && this.clusInfo[0].clus_price) {
        this.price = this.clusInfo[0].clus_price;
        this.clus_price = this.clusInfo[0].clus_price;
      }
      // this.clusInfo[0].clus_price = "正在计算中，请稍后";
      this.getItemComb(this.clusInfo[0].clus_code);
      // this.price = JSON.parse(storage.session.get("clus_price"));
    } catch (error) {
      Toast("数据获取异常！请联系管理员处理");
      this.methodsFlag = false;
    }

  },
  methods: {
    getItemComb(clus_Code) {
      var that = this;
      that.show = true;
      var clus_price = "";
      var pData = {
        comb_code: that.clusInfo[0].clus_code
      };
      ajax
        .post(apiUrls.GetItemCombListQ, pData, { nocrypt: true })
        .then(r => {
          var data = r.data;
          if (data.success) {
            that.combitem = data.returnData;//套餐项目
            // console.log( " that.combitem",that.combitem);
            var pData = {
              comb_code: that.decision().comb_code,
              sex: this.info.sex
            }
            ajax
              .post(apiUrls.GetQuestionItemData, pData, { nocrypt: true })
              .then(r => {
                var data = r.data;
                that.show = false;
                if (data.success) {
                  // console.log("data.returnData",JSON.parse(data.returnData));
                  let combs = JSON.parse(data.returnData);
                  for (var i = 0; i < combs.length; i++) {
                    let f_flag = true;
                    for (var j = 0; j < that.combitem.length; j++) {
                      if (combs[i].comb_code.trim() == that.combitem[j].comb_Code.trim()) {
                        f_flag = false;
                      }
                    }
                    if (f_flag) {
                      that.QuestionItemData.push(combs[i]);
                      this.price = dataUtils.priceSum(this.price, combs[i].price);
                    }
                  }
                  // console.log(" that.QuestionItemData", that.QuestionItemData);
                  that.DaseList = that.QuestionItemData;
                  that.Additemdata = that.QuestionItemData;
                  // this.checkPrice(that.Additemdata)

                  that.show = false;
                  return;
                }
                that.additemShow = true;
                Toast("暂无符合体检项目套餐");
              })
              .catch(e => {
                console.log(e);
                this.methodsFlag = false;
                Toast("网络繁忙~请稍后再试！！！");
              });
          } else {
            this.methodsFlag = false;
            Toast("获取套餐数据异常！");
            return;
          }
        })
        .catch(e => {
          this.methodsFlag = false;
          Toast("网络繁忙~请稍后再试");
        });
    },
    //获取答案
    decision() {
      var itemData = "";
      var addItem = JSON.parse(storage.session.get("QuestionAnswer"));
      // console.log("addItem", addItem);
      try {
        for (var i = 0; i < addItem.length; i++) {
          if (addItem[i].type == "2") {
            if (addItem[i].resultA.comb_type == "T") {
              if (itemData) {
                itemData = itemData + addItem[i].resultA.result_code + ";"
              } else {
                itemData = addItem[i].resultA.result_code + ";"
              }
            }
          } else if (addItem[i].type == "3") {
            addItem[i].resultA.filter(x => {
              if (x.comb_type == "T") {
                if (itemData) {
                  itemData = itemData + x.result_code + ";"
                } else {
                  itemData = x.result_code + ";"
                }
              }
            })
          }



          // if (addItem[i].comb_type == "T") {
          //   for (var iks = 0; iks < addItem[i].answer.length; iks++) {
          //     itemData += ";" + addItem[i].answer[iks].value.trim();
          //   }
          // }
        }
        // console.log("itemData", itemData);
        var pData = {
          comb_code: itemData
        };
        return pData;
      } catch (error) {
        console.log(error)
      }

    },
    changeGroup() {
      this.price = this.clus_price;
      this.Additemdata.filter(x => {
        this.price = dataUtils.priceSum(this.price, x.price);
      })
    },


    //查看套餐项目
    nextgoto() {
      var that = this;
      var dataList = {
        PersonalSpan: that.clusInfo[0].clus_note,
        clus_Code: that.clusInfo[0].clus_code,
        clus_Name: that.clusInfo[0].clus_name,
        price: that.price,
        sex: that.info.sex
      };
      storage.session.set("dataList", JSON.stringify(dataList));
      that.$router.push({
        path: "/questionDetails"
      });
    },

    //获取材料费等
    next() {
      // console.log("this.Additemdata",this.Additemdata);return
      var that = this;
      var dataList = {
        PersonalSpan: that.clusInfo[0].clus_note,
        clus_Code: that.clusInfo[0].clus_code,
        clus_Name: that.clusInfo[0].clus_name,
        price: this.clus_price,
        sex: that.info.sex
      };
      this.dataList=dataList;
      this.syncCombs="";
      this.confirmProj = [];
      let combs = [];
      this.combs=[];
      this.show_material = true;
      this.confirmProj.push({
        clus_name: that.clusInfo[0].clus_name,
        combs: that.combitem,
        price: that.clusInfo[0].clus_price
      })
      this.total = that.clusInfo[0].clus_price;
      if (this.Additemdata.length > 0) {
        combs = this.Additemdata.map(x => {
          return {
            comb_Code: x.comb_code,
            comb_Name: x.comb_name,
            price: x.price
          }
        })
        this.confirmProj.push({
          clus_name: "自选项目",
          combs: combs,
          price: dataUtils.priceSum(this.price, this.clus_price, "T")
        })
        this.combs=combs
        this.GetCaiLF(combs);
      }
      else {
        this.showPop = true;
        this.show_material = false;
      }
      return
    },
    //获取材料费等
    GetCaiLF(combs) {
      let pData = {
        clus_Code: this.clusInfo[0].clus_code,
        choose_comb_code: combs.map(x => x.comb_Code.trim()).join(",")
      }
      // console.log(pData);return
      ajax
        .post(apiUrls.CombsMaterial, pData, { nocrypt: true })
        .then((r) => {
          let data = JSON.parse(r.data.returnData)[0];
          // console.log("data", data);
          if (data.error == "OK") {
            this.total = data.total;
            this.syncCombs = data.add_comb_code;
            setTimeout(() => {
              this.showPop = true;
              this.show_material = false;
            }, 2000);
          } else {
            Toast(data.error);
          }
          return;
        })
        .catch((e) => {
          alert("系统繁忙！请稍后再试");
          this.show_material = false;

        });
    },
    Confirmationsheet() {
      storage.session.set("clusIncludeItem", JSON.stringify(this.combitem));
      storage.session.set("chooseItem", JSON.stringify({
        totalPrice: this.total,
        chooseCombCode: this.combs,
        add_comb_code:this.syncCombs
      }));
      storage.session.set("dataList", JSON.stringify(this.dataList));
      this.$router.push({
        path: "/PersonBookSum"
      });
    },
  }
};
</script>
<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "黑体", "Microsoft Yahei", helvetica, arial;
  -webkit-font-smoothing: antialiased;
  font-size: 10px;
}

div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
blockquote,
p {
  padding: 0;
  margin: 0;
}

table,
td,
tr,
th {
  font-size: 10px;
}

a {
  text-decoration: none;
  color: #000;
}

ol,
ul {
  list-style: none;
}

li {
  list-style-type: none;
  border-bottom: solid 1px #d8d8d8;
}

/*input,select {-webkit-appearance:none;outline: none;}*/
img {
  display: block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: normal;
}

address,
cite,
code,
em,
th,
i {
  font-weight: normal;
  font-style: normal;
}

.clearfix {
  zoom: 1;
}

.clearfix:after {
  display: block;
  overflow: hidden;
  clear: both;
  height: 0;
  visibility: hidden;
  content: ".";
}

/*placeholder -- color*/
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #ccc;
}

input::-ms-input-placeholder,
textarea::-ms-input-placeholder {
  color: #ccc;
}

input::-moz-input-placeholder,
textarea::-moz-input-placeholder {
  color: #ccc;
}

input::input-placeholder,
textarea::input-placeholder {
  color: #ccc;
}

.fl {
  float: left;
  color: #018bf0;
}

.fr {
  float: right;
  color: #018bf0;
}

/*头部部分*/
.headerss {
  width: 100%;
  height: 1.44rem;
  position: relative;
  background: #ffffff;
  box-shadow: 0 1px 8px 0 #dfdfdf;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.headerss>img {
  width: 0.88rem;
  height: 0.88rem;
  display: inline-block;
  // position: absolute;
  // left:.2rem;
  // top:.28rem;
}

.headerss>h2 {
  display: inline-block;
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #9e9e9e;
  width: 1.36rem;
  height: 0.48rem;
  position: absolute;
  top: 0.38rem;
  left: 1.26rem;
}

.headerss>span {
  display: inline-block;
  width: 1.16rem;
  height: 0.42rem;
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #4a4a4a;
  position: absolute;
  top: 0.72rem;
  left: 1.26rem;
}

.headerss>.toggle {
  width: 5.7rem;
  height: 1rem;
  background: url("../../assets/report/viewReportIcon2.png");
  background-size: 100% 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  // position: absolute;
  // left: 2.34rem;
  // top: .22rem;
}

.headerss>.toggle>span {
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #018bf0;
  line-height: 0.32rem;
}

#wrap {
  position: relative;
  width: 100%;
  min-height: 100%;
  height: auto !important;
  height: 100%;
  background-color: #fafafa;
  min-width: 320px;
  max-width: 750px;
  margin: auto;
}

.container {
  padding-bottom: 0.64rem;
}

/*底部版权部分*/
.footer {
  height: 0.64rem;
  line-height: 0.64rem;
  clear: both;
  text-align: center;
  color: #9b9b9b;
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 0.24rem;
}

.footer a {
  color: #9b9b9b;
}

#orderConfirm {
  width: 100%;
  // height: 100%;
  position: absolute;
  background: #fff;
}

header {
  padding: 0.2rem 0 0.2rem 2rem;
  position: relative;
}

header .headPortrait {
  width: 1.28rem;
  height: 1.28rem;
  position: absolute;
  top: 0.28rem;
  left: 0.34rem;
  text-align: center;
  font-size: 0.28rem;
  color: #4a4a4a;
}

header .headPortrait img {
  margin-bottom: 0.1rem;
}

// header .headPortrait span {

// }
header .information span {
  display: block;
  line-height: 0.46rem;
  color: #9e9e9e;
  font-size: 0.28rem;
}

header .information i {
  color: #4a4a4a;
  margin-left: 0.36rem;
}

.packageList {
  padding-bottom: 0.98rem;
}

.packageList .listTitle {
  padding: 0 0.24rem;
  line-height: 0.48rem;
  font-size: 0.22rem;
  color: #fff;
  background-color: #018bf0;
}

.packageList .listInfor {
  position: relative;
}

.packageList .listInfor a {
  display: block;
}

.packageList section:nth-child(1) .listInfor,
.packageList section:nth-child(2) .listInfor {
  // line-height: 0.88rem;
  padding: 0 0.24rem;
  font-size: 0.24rem;
}

.packageList .direction {
  position: absolute;
  right: 0.24rem;
  top: 0;
  width: 0.12rem;
  height: 0.88rem;
  line-height: 0.88rem;
}

.packageList .direction img {
  display: inline;
  vertical-align: middle;
}

// .packageList .obvious .listInfor div {
// padding-left: 0.48rem;
// }
.packageList .obvious .listInfor h4 {
  border-bottom: solid #018bf0;
  line-height: 0.48rem;
  color: #7c7c7c;
  font-size: 0.25rem;
  padding-right: 0.24rem;
}

.packageList .obvious .listInfor div h5 {
  height: 0.64rem;
  line-height: 0.64rem;
  padding-left: 0.18rem;
  font-size: 0.22rem;
  color: #9b9b9b;
  position: relative;
}

.packageList .obvious .listInfor div h5 img {
  position: absolute;
  width: 0.24rem;
  height: 0.12rem;
  top: 50%;
  margin-top: -0.06rem;
  right: 0.24rem;
}

.packageList .obvious .listInfor div li p {
  padding: 0 0.36rem;
  font-size: 0.22rem;
  line-height: 0.32rem;
  color: #9b9b9b;
  display: none;
}

.affirmBox {
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 0.16rem 0.18rem;
  border-top: 0.02rem solid #d3d3d3;
  height: 0.98rem;
  box-sizing: border-box;
  background-color: rgba(255, 255, 255, 0.9);
  color: #4a4a4a;
  overflow: hidden;
}

.affirmBox h5 {
  line-height: 0.64rem;
  font-size: 0.24rem;
  border-right: 0.04rem solid #9b9b9b;
  padding-right: 0.2rem;
  margin-right: 0.16rem;
  float: left;
}

.affirmBox p {
  float: left;
  font-size: 0.28rem;
  line-height: 0.64rem;
}

.affirmBox p span {
  display: block;
}

.affirmBox .coicksum {
  float: right;
  width: 1.76rem;
  height: 0.64rem;
  text-align: center;
  line-height: 0.64rem;
  background-color: #018bf0;
  border-radius: 0.16rem;
  font-size: 0.28rem;
  color: #fff;
}

.affirmBox .coicksum {
  margin-left: 0.16rem;
}

// .affirmBox .coicksum {
// background-color: #ef809b;
// }
.addcomb {
  float: right;
  width: 1.6rem;
  height: 0.64rem;
  text-align: center;
  line-height: 0.64rem;
  background-color: #018bf0;
  border-radius: 0.16rem;
  font-size: 0.28rem;
  color: #fff;
  margin-left: 0.16rem;
}

/*确认订单*/
#orderConfirm .packageList .direction {
  position: absolute;
  width: 0.24rem;
  height: 0.12rem;
  top: 50%;
  margin-top: -0.06rem;
  right: 0;
}

#orderConfirm .packageList .listTitle {
  color: #018bf0;
  background-color: #fff;
  padding-left: 0;
  margin-left: 0.24rem;
  // border-bottom: 0.02rem solid #018bf0;
}

#orderConfirm .packageList .listInfor a {
  position: relative;
  line-height: 0.88rem;
  padding-left: 0.4rem;
}

#orderConfirm .packageList .listInfor p {
  line-height: 0.32rem;
  padding-bottom: 0.28rem;
  padding-left: 0.4rem;
  display: none;
}

#orderConfirm .packageList .direction img {
  display: block;
  //   vertical-align: inherit;
}

/*确认订单 -- 抵扣卡弹出块*/
.popCoupon {
  position: fixed;
  width: 100%;
  top: 1.54rem;
  left: 0;
  z-index: 2;
  box-sizing: border-box;
  display: none;
}

.mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.6);
  display: none;
}

.popCoupon .packet {
  width: 100%;
  box-sizing: border-box;
  padding: 0 16% 0.96rem;
}

.popCoupon .title,
.popCoupon .couponBtn {
  width: 100%;
  height: 0.88rem;
  line-height: 0.88rem;
  text-align: center;
  background-image: linear-gradient(-180deg, #e42076 0%, #ec1f3e 97%);
  color: #fff;
  font-size: 0.36rem;
  border-radius: 0.16rem;
}

.popCoupon .couponBanner {
  position: relative;
  width: 100%;
  height: 3rem;
  overflow: hidden;
}

.popCoupon .couponBanner .inner {
  height: 100%;
  position: absolute;
}

.popCoupon .couponBanner .inner div {
  height: 100%;
  text-align: center;
  font-size: 32px;
  color: #fff;
  float: left;
  background: url(../../assets/question/coupon.png) 0% 0%/100% 100% no-repeat;
  border-radius: 0.16rem;
  color: #fff;
}

.popCoupon .couponBanner .inner div span {
  display: block;
}

.popCoupon .couponBanner .inner div span:nth-child(1) {
  font-size: 0.72rem;
  margin-top: 0.72rem;
}

.popCoupon .couponBanner .inner div span:nth-child(2) {
  font-size: 0.48rem;
}

.popCoupon p {
  line-height: 0.72rem;
  text-align: center;
  font-size: 0.28rem;
  color: #fff;
  padding-bottom: 0.8rem;
}

.popCoupon .couponBtn {
  width: 64%;
  background-color: #f64772;
  background-image: none;
  margin-bottom: 0;
  margin: auto;
}

.detailed {
  width: 92%;
  margin: 0 auto;
  border-bottom: 1px solid #dfe3e9;
}

.detailed .detailedTitle {
  width: 100%;
  min-height: 0.6rem;
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: 0.1rem;
}

.detailed .detailedText {
  color: #656262;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin-left: 0.35rem;
}

.detailed .detailedBottom {
  width: 100%;
  height: 0.45rem;
  font-size: 14px;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.detaDiy {
  width: 20%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detaDiyTwe {
  width: 55%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}

.detaDiyThree {
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detaDiyFive {
  width: 62%;
  background: #018bf0;
  line-height: 0.7rem;
  display: flex;
  justify-content: center;
  border-radius: 7px;
  color: #fff;
}

.detaDiyFiert {
  font-size: 14px;
  line-height: 0.4rem;
  margin-top: 0.1rem;
  text-indent: 28px;
}

.detaDiySei {
  color: red;
  font-size: 14px;
  margin-top: 0.2rem;
}

.noeDate {
  display: flex;
  width: 100%;
  // line-height: 1rem;
}

.noeDate span {
  // width: 20%;
  text-align: center;
  font-size: 14px;
  color: red;
}

.noeDate .van-checkbox {
  // width: 80% !important;
  font-size: 13px !important;
  width: 100%;
  /* line-height: 21px; */
  /* display: flex; */
  min-height: 0.7rem;
}

.selTOne {
  border-bottom: 1px solid #cccaca;
}

.selTWe {
  margin-left: .59rem;
}

.selTWes {
  font-size: 13px;
  line-height: .4rem;
}
</style>
<style lang="scss">
.vancell {
  border-bottom: 1px solid #b0b0b0;
}

.vancell .van-collapse-item__content {
  padding: 0px !important;
  color: #4c3f3f !important;
}


@mixin flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.showBox {
  // width: 95%;
  max-height: 60vh;
  margin-left: 5%;
  overflow-y: auto;
  color: #4a4a4a;

  .showTitles {
    width: 100%;
    height: 0.8rem;
    @include flex();
    font-size: 0.36rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
  }

  // 统计
  .total {
    width: 100%;
    height: 1.5rem;
    margin-top: 0.4rem;
    box-shadow: 3px 3px 7px #888888;
    border-radius: 5px;
    padding-bottom: 0.2rem;
    @include flex();
    font-size: 0.32rem;

    .totalProject {
      width: calc(50% - 0.5px);
      height: 100%;

      .totalText1 {
        width: 100%;
        height: 50%;
        @include flex();
        align-items: flex-end;
      }

      .totalText2 {
        width: 100%;
        height: 50%;
        @include flex();
        color: red;
      }
    }

    .hr {
      height: 70%;
      width: 1px;
      background: #dcdfe6;
    }
  }

  // 列表
  .showLists {
    width: 100%;
    font-size: 0.28rem;
    color: #4a4a4a !important;
    padding: 5px 0;
    box-shadow: 3px 3px 7px #888888;
    margin-top: 0.2rem;
    border-radius: 5px;
    @include flex();
    flex-direction: column;

    .showTitle {
      width: 100%;
      height: 0.8rem;
      @include flex();
      border-bottom: 1px solid #e5e5e5;
      font-weight: 600;
      font-size: 0.36rem;
    }

    .showText {
      width: 100%;
      display: flex;
      margin-top: 0.1rem;
      margin-bottom: 0.1rem;

      .showNo1 {
        width: 80%;
        min-height: 0.6rem;
        @include flex();
        justify-content: flex-start;
        padding-left: 0.2rem;
      }

      .showNo2 {
        width: 20%;
        min-height: 0.6rem;
        @include flex();
        justify-content: flex-start;
        padding-left: 0.2rem;
        color: red;
      }
    }
  }
}

.toBox {
  width: 100%;
  height: 1.4rem;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .toGo {
    width: 40%;
    height: 0.8rem;
    color: #4a4a4a;
    border-radius: 5px;
    @include flex();
    font-size: 0.36rem;
    background: white;
    border: 1px solid #e5e5e5;
    letter-spacing: 1px;
  }


  .toGreen {
    background: #409eff !important;
    color: white;
  }
}
</style>