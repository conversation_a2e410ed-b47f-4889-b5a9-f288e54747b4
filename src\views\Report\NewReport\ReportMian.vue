<template>
  <div id="reportWrap">
    <!--页面头部-->
    <div class="header">
      <!--用户头像-->
      <div class="user_picture">
        <img src="../../../assets/report/user_pr.png" alt />
      </div>
      <!--用户身份信息-->
      <div class="user_item">
        <ul>
          <li>
            <h3>姓名</h3>
            <span>{{ user.report_Name }}</span>
          </li>
          <li>
            <h3>性别</h3>
            <span v-if="user.report_Sex == 1">男</span>
            <span v-if="user.report_Sex == 2">女</span>
          </li>
          <li>
            <h3>年龄</h3>
            <span>{{ user.report_Age }}</span>
          </li>
        </ul>
      </div>
    </div>
    <!--用户病例信息-->
    <div class="contanier">
      <div class="user_message">
        <ul>
          <li>
            <h2>单位名称</h2>
            <span>|</span>
            <p>{{ baseData.hospitalName }}</p>
          </li>
          <li>
            <h2>体检机构</h2>
            <span>|</span>
            <p>健康医学科</p>
          </li>
          <li>
            <h2>体检日期</h2>
            <span>|</span>
            <p>{{ user.report_Date }}</p>
          </li>
          <li>
            <h2>体检编号</h2>
            <span>|</span>
            <p>{{ user.report_No }}</p>
          </li>
        </ul>
      </div>
    </div>
    <!--主页面导航-->
    <div class="nav">
      <div class="item" @click="gotourl('/reportguide')">
        <img src="../../../assets/report/introduction.png" alt />
        <a href="javascript:void(0)">报告导读</a>
      </div>
      <div class="item" @click="gotourl('/ReportFinal')">
        <img src="../../../assets/report/report2.png" alt />
        <a href="javascript:void(0)">总检报告</a>
      </div>
      <div class="item" @click="gotourl('/ReportSuggest')">
        <img src="../../../assets/report/suggest.png" alt />
        <a href="javascript:void(0)">医生建议</a>
      </div>
      <div class="item" @click="gotourl('/ReportDetails')">
        <img src="../../../assets/report/norm.png" alt />
        <a href="javascript:void(0)">指标细节</a>
      </div>
      <div
        class="item"
        v-if="report.tj_cls != '1' && report.tj_cls != '2'"
        @click="reportPDF()"
      >
        <img src="../../../assets/report/downloadPdf.png" alt />
        <a href="javascript:void(0)">PDF下载</a>
      </div>
      <!-- <div class="item"  v-if="showMail" @click="gotourl('/report/reportMail/'+reportNo)" >
            <img src="@/assets/img/report2.png" alt="">
            <a href="javascript:void(0)">邮寄信息</a>
      </div>-->
    </div>
    <!--页面尾部-->
    <div class="foot">
      <span
        >注意：为了保障你的隐私资料，请勿随意传阅，最终体检结果以纸质报告单为准</span
      >
    </div>

    <!--网页尾部-->
    <div class="footer">
      <div class="footer_left" @click="gotourl('/ReportList')">
        <img src="../../../assets/report/repotlist.png" alt />
        <span>报告列表</span>
      </div>
    </div>
  </div>
</template>

<script>
import { storage, ajax, toolsUtils } from "../../../common";
import apiUrls from "../../../config/apiUrls";
import Vue from "vue";
import { Toast } from "vant";
//import baseData from '../../../config/baseData';

export default {
  components: {},
  data() {
    return {
      baseData: Vue.prototype.baseData,
      report: {
        name: "nana",
        sex: "1",
        age: 18,
      },
      user: [],
      reportNo: "",
      showMail: false,
    };
  },
  created() {
    try {
      //  var user=JSON.parse(storage.cookie.get("user"));
      this.user = JSON.parse(storage.session.get("pat_info"));
    } catch (error) {
      this.$router.push({ path: "/oauth?type=jump" });
    }
    // this.reportNo=toolsUtils.decrypt(this.$route.params.no,this.user.pat_code.trim()+'nysyt');
    this.GetReportDetail(this.user.report_No);
  },
  mounted() {},
  methods: {
    GetReportDetail(report_No) {
      var pData = {
        regNo: report_No,
      };

      ajax
        .post(apiUrls.GetReportDetail, pData, { nocrypt: true })
        .then((r) => {
          if (r.data.success) {
            var data = JSON.parse(r.data.returnData);
            // console.log("data",data);
            this.report = data;
            storage.session.set("report", JSON.stringify(data[0]));
          } else {
            Toast(r.data.returnMsg);
          }
          return;
        })
        .catch((err) => {
          Toast("网络异常");
          console.log(err);
        });
    },
    gotourl(url) {
      this.$router.push({ path: url });
    },
    reportPDF() {
      if (
        this.report == null ||
        this.report == "" ||
        this.report == undefined
      ) {
        return;
      }

      //获取服务器时间戳
      ajax
        .get(apiUrls.GetTimeStamp)
        .then((r) => {
          r = r.data;
          if (r.success) {
            var timenum = r.returnData;
            var pdfUrl = this.report[0].regno.trim() + ".pdf";
            // alert(pdfUrl);
            // return;
            var url = encodeURIComponent(
              toolsUtils.encrypt(pdfUrl + "/" + timenum)
            );

            var that = this;
            setTimeout(function () {
              //原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
              window.location.href =
                that.baseData.apiHost + "/home/<USER>" + url; //改变页面的location
            }, 300);
          } else {
            Toast("网络异常");
          }
        })
        .catch((err) => {
          Toast("网络异常");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
h1,
h2,
h3,
h4,
h5 {
  margin: 0;
}
#reportWrap {
  width: 100%;
  height: 100%;
  background-color: #f5f6f7;
}
/*页面头部*/
#reportWrap .header {
  width: 100%;
  height: 1.76rem;
  background: url(../../../assets/report/<EMAIL>);
  background-size: cover;
}

/*用户头像*/
#reportWrap .header .user_picture {
  width: 0.96rem;
  height: 0.96rem;
  overflow: hidden;
  position: absolute;
  margin-left: 0.4rem;
  margin-top: 0.4rem;
}

/*用户身份信息*/
#reportWrap .header .user_item {
  position: absolute;
  margin-left: 1.72rem;
  margin-top: 0.2rem;
}
#reportWrap .header .user_item > ul {
  overflow: hidden;
}
#reportWrap .header .user_item > ul > li {
  display: inline-block;
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #ffffff;
  line-height: 0.48rem;
}
#reportWrap .header .user_item > ul > li span {
  font-size: 0.32rem;
  height: 0.48rem;
}
#reportWrap .header .user_item > ul > li:nth-child(3n + 2) {
  margin: 0 1rem;
}

/*用户病例信息*/
.contanier {
  width: 100%;
  height: 4rem;
  background: #ffffff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}
.user_message {
  position: absolute;
  margin-left: 0.36rem;
  margin-top: 0.16rem;
}
.user_message ul li {
  width: 7.02rem;
  height: 0.88rem;
  font-size: 0.28rem;
  font-family: PingFangSC-Regular;
  color: #4a4a4a;
}
.user_message ul li h2 {
  display: inline-block;
  line-height: 0.88rem;
  font-size: 0.28rem;
  color: #7a7a7a;
}
.user_message ul li span {
  display: inline-block;
  line-height: 0.88rem;
  font-size: 0.28rem;
  color: #7a7a7a;
  margin: 0 0.2rem;
}
.user_message ul li p {
  display: inline-block;
  line-height: 0.88rem;
  font-size: 0.28rem;
  color: #4a4a4a;
}

/*主页面导航*/
.nav {
  /* width:7.5rem; */
  width: 100%;
  /* height: 2.4rem; */
  overflow: hidden;
  margin-top: 0.24rem;
  text-align: center;
}
.nav .item {
  /* width:calc(7.5rem/2); */
  width: 50%;
  height: 1.2rem;
  float: left;
  opacity: 0.99;
  font-family: PingFangSC-Medium;
  font-size: 0.32rem;
  color: #4a4a4a;
  background: #ffffff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.nav .item img {
  width: 0.48rem;
  height: 0.48rem;
  display: inline-block;
}
.nav .item a {
  height: 0.44rem;
  display: inline-block;
  margin-left: 0.28rem;
}

/*页面尾部*/
.foot {
  width: 7.02rem;
  height: 1rem;
  margin: 0 auto;
  position: relative;
  top: 0.22rem;
}
.foot span {
  font-family: PingFangSC-Regular;
  font-size: 0.26rem;
  color: #9b9b9b;
  display: block;
}

.footer {
  width: 7.5rem;
  height: 0.88rem;
  position: fixed;
  bottom: 0;
  left: 0;
}
.footer .footer_left {
  width: 100%;
  height: 0.88rem;
  float: left;
  border-right: 1px solid white;
  background-color: #489eea;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.footer .footer_left img {
  width: 0.356rem;
  height: 0.304rem;
  padding-right: 0.1rem;
  display: inline-block;
}
.footer .footer_left span {
  font-family: PingFangSC-Medium;
  display: inline-block;
  height: 0.44rem;
  line-height: 0.44rem;
  font-size: 0.32rem;
  color: #ffffff;
}
</style>
