<template>
  <div style class="qusetinoheard">
    <div id="dcwq" class="whole">
      <!--头部信息-->
      <div class="head">
        <div class="t_pic">
          <img src="../../assets/HomeLogo.png" />
        </div>
        <div class="t_tit">
          <p>健康体检基本项目专家共识</p>
        </div>
        <!-- <h3
          style="padding-top: 40px;padding-left: 25vw;font-size: 130%;color:white"
        >{{questionList[chooseIndex].htitle}}</h3>-->
      </div>

      <!--选择单选题目-->
      <div class="t_con">
        <div class="con_tit">
          <!-- <b v-if="questionData.overst_type=='T'" style="color: #cc3736;font-size: 0.33rem;">
            {{questionData.question_name}}
            <span v-if="questionData.phy_exp!=null">({{questionData.phy_exp}})</span>
          </b> -->
          <p>
            {{ questionData.question_name }}
            <!-- <span v-if="questionData.phy_exp!=null">({{questionData.phy_exp}})</span> -->
            <span v-if="questionData.phy_exp">({{questionData.phy_exp}})</span>
            <span v-if="questionData.type == '3'">(多选)</span>
            <span v-if="questionData.type == '2'">(单选)</span>
            <span v-if="questionData.type == '1'">(填空)</span>
          </p>
        </div>
        <div class="con_con">
          <ul>
            <li v-for="(item, index) in questionData.answers" :key="index">
              <div v-if="questionData.type == '1'" style="display: flex;">
                <van-field v-model="questionData.resultA" :label="item.result_name" left-icon="smile-o"
                  placeholder="请输入信息" />
              </div>
              <div v-else-if="questionData.type == '2'">
                <van-radio-group v-model="questionData.resultA">
                  <van-radio :name="item" @click="chooseRadio(questionData, item)">{{ item.result_name
                  }}</van-radio>
                </van-radio-group>
              </div>
              <div v-else-if="questionData.type == '3'">
                <van-checkbox-group v-model="questionData.resultA">
                  <van-checkbox :name="item" @click="chooseGroup(questionData)">{{ item.result_name
                  }}</van-checkbox>
                </van-checkbox-group>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <!--上提下题按钮-->
      <div class="t_btn">
        <van-button color="linear-gradient(to right, rgb(231 145 57), rgb(238 136 10))"
          v-if="questionData && mainQ[0] && mainQ[0].question_code != questionData.question_code" @click="gotoQusestion()">
          返回上一题
        </van-button>
        <van-button color="linear-gradient(to right, #2196f3, #2196f3)" @click="nextQuestions()">
          下一题
        </van-button>
      </div>
      <!--提示遮罩层-->
      <!-- <div class="ts_mask">
        <div class="tishi">
          <div class="qd" @click="maskfn()"></div>
        </div>
      </div> -->
    </div>
    <!--遮罩层-->
    <van-overlay :show="show" v-show="show">
      <div class="vanoverBtn">
        <van-loading type="spinner" color="#1989fa">获取中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { ajax, storage } from "../../common";
import { Dialog, Toast } from "vant";
import apiUrils from "../../config/apiUrls";
import $ from "jquery";
export default {
  data() {
    return {
      message: "",
      shows: false,
      show: false,
      isnext: false,

      questionList: [],
      chooseIndex: 0,
      chooseItemIndex: -1,
      oneAnswerData: { qno: "", answer: [], cft_code: "", qt_code: "" },
      ResulAnswer: [],
      xiyan: "",
      addItemFlag: [],
      questionInfo: [],
      type: "",
      hobby2: [],//多项选择的数组

      tiankon: "",//填空答案
      questionData: {},//问题
      result: [],//多选择题答案
      radio: "",//单选答案
      questionAnswer: [],//问卷答案
      ansOrder: [],//问题记录（不记录当前问题，即在下一个问题开始记录上一个问题的序号）

      mainQ: [],//主要问题
      secondaryQ: [],//关联问题
      answers: [],//选中答案
      glwts: [],//存放关联问题
      glwtsIndex: -1,//关联问题序号
      wtIndex: 0,//问题序号

    };
  },
  created() {
    this.questionInfo = JSON.parse(storage.session.get("questionInfo"));
    // this.type = storage.session.get("type");
    this.type =  this.$route.query["question"];
    this.GetQuestionData();
  },
  // computed: {
  //   GetAnswers() {
  //     this.answers = [];
  //     if (this.mainQ.length > 0) {
  //       this.mainQ.filter(x => {
  //         if (x.resultA && x.resultA.length > 0) {
  //           this.answers.push({
  //             question_name: x.question_name,
  //             question_code: x.question_code,
  //             resultA: x.resultA,
  //             question_order: x.question_order
  //           })
  //         }
  //       })
  //     }
  //     if (this.secondQues.length > 0) {
  //       this.secondQues.filter(x => {
  //         if (x.resultA && x.resultA.length > 0) {
  //           this.answers.push({
  //             question_name: x.question_name,
  //             question_code: x.question_code,
  //             resultA: x.resultA,
  //             question_order: x.question_order
  //           })
  //         }
  //       })
  //     }
  //     console.log("this.answers", this.answers);

  //   },

  // },
  mounted() {
    window.addEventListener("scroll", this.scrollToTop);
  },
  destroyed() {
    window.removeEventListener("scroll", this.scrollToTop);
  },
  methods: {
    //获取调查问卷题目
    GetQuestionData() {
      var that = this;
      that.show = true;
      var pData = {
        sex: this.questionInfo.sex
      };
      ajax
        .post(apiUrils.GetQuestionData, pData, { nocrypt: true })
        .then(r => {

          if (r.data.success) {
            that.mainQ = JSON.parse(r.data.returnData).mainQ.map(x => {
              if (x.type == "3") {
                return {
                  ...x, resultA: []
                };
              } else {
                return x;
              }
            });
            that.secondaryQ = JSON.parse(r.data.returnData).secondaryQ.map(x => {
              if (x.type == "3") {
                return {
                  ...x, resultA: []
                };
              } else {
                return x;
              }
            });
          }
          // console.log("that.secondaryQ", that.secondaryQ);

          //初始化答案
          that.radio = "";
          that.result = [];
          // that.questionList = that.mainQ;
          that.questionData = that.mainQ[that.wtIndex];

          setTimeout(function () {
            that.show = false;
          }, 500);
          // this.allanimate();
        })
        .catch(e => {
          alert("网络繁忙！请稍后再试");
        });
    },
    //单选题；
    chooseRadio(question, item) {
      let that = this;
      that.show = true;
      let a = that.answers.filter(x => x.question_code != question.question_code);
      that.answers = a;
      if (question.resultA) {
        //把问题和答案放进that.answers
        that.answers.push({
          question_code: question.question_code,
          resultA: question.resultA,
          type: question.type,
          binding_code: question.binding_code
        })
        //判断单选的答案是否有关联问题
        if (item.result_el_code) {
          for (let i = 0; i < that.secondaryQ.length; i++) {
            let x = that.secondaryQ[i];
            if (x.binding_code == item.result_el_code) {
              if (that.glwts.length == 0) {
                that.glwtsIndex = -1;
              }
              that.glwts.push(x);
            }
          }
        } else {
          if (that.glwts.length > 0) {
            //排除该问题所有答案的关联问题
            let paichu = [];
            for (let j = 0; j < that.glwts.length; j++) {
              let gl_flag = true;
              question.answers.filter(x => {
                if (x.result_el_code && x.result_el_code == that.glwts[j].binding_code) {
                  gl_flag = false;
                }
              })
              if (gl_flag) {
                paichu.push(that.glwts[j])
              }
            }
            that.glwts = paichu;
          } else {
            that.glwtsIndex = -1;
          }
        }
        //是否有关联问题，有则下一题为关联问题
        if (that.glwts.length > 0) {
          that.glwtsIndex++;
          that.questionData = that.glwts[that.glwtsIndex];
          if (that.glwts.length === (that.glwtsIndex + 1)) {
            that.glwtsIndex = -1;
            that.glwts = [];
          }
        } else {
          if ((that.wtIndex + 1) != that.mainQ.length) {
            that.wtIndex++;
            that.questionData = that.mainQ[that.wtIndex];
          } else {
            that.showTips();
          }
          // that.wtIndex++;
          // that.questionData = that.mainQ[that.wtIndex];
        }
      }
      that.show = false;
      // console.log("this.answers", this.answers[0].resultA);
    },
    //多选填
    chooseGroup(question) {
      // let that = this;
      // let a = that.answers.filter(x => x.question_code != question.question_code);
      // that.answers = a;
      // if (question.resultA.length > 0) {
      //   //把问题和答案放进that.answers
      //   that.answers.push({
      //     question_code: question.question_code,
      //     resultA: question.resultA,
      //     type: question.type
      //   })
      // }
    },
    //下一个问题
    nextQuestions() {
      let that = this;
      that.show = true;
      let nextQ = false;//是否答完问卷
      let nowQuest = that.questionData;//现在的问题
      if (nowQuest.type === "3") {
        if (nowQuest.resultA.length === 0) {
          Toast("请完成多选题");
          return;
        } else {

          //把问题和答案放进that.answers
          let al = that.answers.filter(x => x.question_code != nowQuest.question_code);
          that.answers = al;
          that.answers.push({
            question_code: nowQuest.question_code,
            resultA: nowQuest.resultA,
            type: nowQuest.type,
            binding_code: nowQuest.binding_code
          })

          //判断选的答案是否有关联问题
          nowQuest.resultA.filter(x => {
            if (x.result_el_code) {
              for (let i = 0; i < this.secondaryQ.length; i++) {
                if (x.result_el_code == this.secondaryQ[i].binding_code) {
                  that.glwts.push(this.secondaryQ[i])
                }
              }
            }
          })
          //是否有关联问题，有则下一题为关联问题
          if (that.glwts.length > 0) {
            that.glwtsIndex++;
            that.questionData = that.glwts[that.glwtsIndex];
            if (that.glwts.length === (that.glwtsIndex + 1)) {
              that.glwtsIndex = -1;
              that.glwts = [];
            }
          } else {
            if ((that.wtIndex + 1) == that.mainQ.length) {
              nextQ = true;
            } else {
              that.wtIndex++;
              that.questionData = that.mainQ[that.wtIndex];
            }
          }

        }
      }
      //填空
      else if (that.questionData.type === "1") {
        if (nowQuest.resultA.length === 0) {
          Toast("请完问题");
          return;
        } else {
          //把问题和答案放进that.answers
          let al = that.answers.filter(x => x.question_code != nowQuest.question_code);
          that.answers = al;
          that.answers.push({
            question_code: nowQuest.question_code,
            resultA: nowQuest.resultA,
            type: nowQuest.type,
            binding_code: nowQuest.binding_code
          })

          //是否有关联问题，有则下一题为关联问题
          if (that.glwts.length > 0) {
            that.glwtsIndex++;
            that.questionData = that.glwts[that.glwtsIndex];
            if (that.glwts.length === (that.glwtsIndex + 1)) {
              that.glwtsIndex = -1;
              that.glwts = [];
            }
          } else {
            if ((that.wtIndex + 1) == that.mainQ.length) {
              nextQ = true;
            } else {
              that.wtIndex++;
              that.questionData = that.mainQ[that.wtIndex];
            }
          }
        }

      }
      //单选
      else if (that.questionData.type === "2") {
        this.chooseRadio(that.questionData, that.questionData.resultA)
      }

      if (that.glwts.length === 0 && (that.wtIndex + 1) == that.mainQ.length) {
        nextQ = true;
      }

      if (nextQ) {
        that.showTips();
      }
      that.show = false;
      // console.log("that.answers",that.answers);

    },
    //返回上一题
    gotoQusestion() {
      let that = this;
      that.show = true;
      let ans_lis = JSON.parse(JSON.stringify(that.answers));
      let item = ans_lis.pop();
      if (that.glwts.length > 0) {
        let b = [];
        //除去与上个问题有关的关联问题
        for (let i = 0; i < that.glwts.length; i++) {
          let gl_flag = true;
          if (item.type == "3") {
            item.resultA.filter(x => {
              if (x.result_el_code && x.result_el_code == that.glwts[i].binding_code) {
                gl_flag = false;
              }
            })
          } else if (item.type == "2") {
            if (item.resultA.result_el_code && item.resultA.result_el_code == that.glwts[i].binding_code) {
              gl_flag = false;
            }
          }
          if (gl_flag) {
            b.push(that.glwts[i])
          }
        }
        that.glwts = b;
      } else {
        that.glwtsIndex = -1;
        that.glwts = [];
      }
      if (item.binding_code) {
        let aa = this.secondaryQ.filter(x => x.question_code == item.question_code);
        that.questionData = aa[0];
        that.answers.pop();
      } else {
        if (!that.questionData.binding_code) {
          that.wtIndex--;
        }
        that.questionData = this.mainQ[that.wtIndex];
        that.answers.pop();
      }
      that.show = false;
    },
    showTips() {
      // console.log("that.answers", this.answers);
      Dialog.confirm({
        title: "提示",
        message: "您已完成健康调查，感谢您的支持",
        confirmButtonText: '确定',
        cancelButtonText: '返回'
      }).then(() => {
        storage.session.set("QuestionAnswer", JSON.stringify(this.answers));
        this.$router.push("/questionords");
        return;
      })
        .catch(() => {
          // that.questionData = nowQuest;
          // that.ansOrder.pop();
          return
        });
    },
    //关闭提示
    // maskfn() {
    //   $(".ts_mask").fadeOut(500);
    // },
  }
};
</script>
<style lang="scss" scoped>
body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
form,
input,
legend,
button,
textarea {
  margin: 0;
  padding: 0;
  vertical-align: baseline;
  background: transparent;
  border: 0;
  outline: 0;
}

ul,
li {
  float: left;
  font-family: "微软雅黑";
  color: #6e5b2e;
}

body,
button,
input,
select,
textarea {
  font: 12px/1.5 tahoma, arial, \5b8b\4f53, sans-serif;
  background: #3299de;
  font-family: "微软雅黑";
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}

address,
cite,
dfn,
em,
var {
  font-style: normal;
}

code,
kbd,
pre,
samp {
  font-family: courier new, courier, monospace;
}

small {
  font-size: 12px;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

a:visited {
  color: #000;
}

sup {
  vertical-align: text-top;
}

sub {
  vertical-align: text-bottom;
}

legend {
  color: #000;
}

fieldset,
img {
  border: 0;
}

legend {
  color: #000;
}

fieldset,
img {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

input[type="checkbox"] {
  -webkit-appearance: checkbox !important;
  appearance: checkbox !important;
}

button,
input,
select,
textarea {
  font-size: 100%;
  visibility: hidden;
  -webkit-appearance: none;
  appearance: none;
}

* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.qusetinoheard {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow-y: auto;
  font: 12px/1.5 tahoma, arial, \5b8b\4f53, sans-serif;
  background: #fafafa;
  font-family: "微软雅黑";
}

/*调研首页*/
.whole {
  width: 100%;
  height: auto;
  margin: 0 auto;
  position: relative;
}

.content {
  width: 80%;
  height: auto;
  margin: 10vw 10% 0 10%;
}

.dc_tit {
  width: 100%;
  height: 30vw;
  position: relative;
  float: left;
  margin-left: -77vw;
}

.dc_tit img {
  width: 100%;
  height: auto;
}

.dc_pic {
  width: 100%;
  height: 80vw;
  position: relative;
  float: left;
  margin-bottom: 0vw;
}

.dc_pic img {
  width: 100%;
  height: auto;
}

.dc_btn {
  width: 100%;
  height: 20vw;
  position: relative;
  float: left;
  margin-left: 77vw;
}

.dc_btn input {
  width: 100%;
  height: 12vw;
  background: #fff;
  margin-top: 4vw;
  border-radius: 5px;
  font-size: 5vw;
  text-align: center;
  line-height: 12vw;
  -webkit-appearance: none;
  appearance: none;
  visibility: visible;
}

.dc_btn input span {
  font-size: 5vw;
}

.foot {
  width: 100%;
  height: 10vw;
  text-align: center;
  font-size: 3vw;
  color: #000;
  line-height: 10vw;
  float: left;
  margin-top: 8vw;
}

/*首页2*/
.dc_tit1 {
  width: 80%;
  height: 17vh;
  float: left;
  margin-left: 10vw;
  margin-top: 17vh;
  color: white;
  font-size: 3rem;
  text-align: center;
  position: relative;
  background: #0099ff;
}

.dc_tit1 img {
  width: 80%;
  height: auto;
  margin-left: 10%;
}

.pc_img {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  position: absolute;
}

.pc_img img {
  width: 100%;
  height: auto;
}

.btn {
  width: 70%;
  height: 12vw;
  position: absolute;
  top: 128vw;
  left: 15vw;
}

/*调研内容页面*/
.head {
  width: 95%;
  height: 20vw;
  margin: 8vw auto 2vw 5%;
}

.t_pic {
  width: 20vw;
  height: 20vw;
  float: left;
  border-radius: 10px;
  overflow: hidden;
}

.t_pic img {
  width: 100%;
  height: auto;
}

.t_tit {
  width: 70vw;
  height: 8vw;
  margin-top: 1vw;
  float: right;
  border-bottom: 1px solid #b3b0b0;
  font-size: 4vw;
  color: #414141;
  letter-spacing: 0.5vw;
  overflow: hidden;
}

.t_tit p {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.t_con {
  width: 100%;
  height: auto;
  min-height: 55vh;
  margin: 0 auto;
  overflow: hidden;
  background: url(../../assets/question/con_bg.png) right no-repeat;
  background-size: 50% auto;
}

.con_tit {
  width: 80%;
  height: auto;
  margin: 0 10%;
  color: #414141;
  position: relative;
  // margin-left: -70vw;
}

.con_tit p:nth-of-type(1) {
  font-size: 4.5vw;
  line-height: 5vw;
}

.con_tit p:nth-of-type(2) {
  font-size: 6vw;
  line-height: 10vw;
}

.con_con {
  width: 100%;
  height: auto;
  margin: 0 auto;
  float: left;
  position: relative;
}

.con_con ul {
  width: 80%;
  height: auto;
  margin: 6vw 10% 0 10%;
  overflow: hidden;
  position: relative;
}

.con_con ul li {
  width: 100%;
  height: auto;
  float: left;
  // top: 80vw;
  position: relative;
  color: #414141;
  font-size: 4.6vw;
  line-height: 6vw;
  margin: 3vw auto;
  z-index: 9;
}

.con_con ul li p {
  margin-left: 9vw;
}

.t_btn {
  width: 90%;
  height: 18vw;
  float: left;
  margin: 5vw 5% 10vw 5%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.t_btn button {
  width: 39%;
  height: 12vw;
  float: left;
  font-size: 4vw;
  text-align: center;
  line-height: 12vw;
  border-radius: 10px;
  visibility: visible;
}

.t_btn button:nth-of-type(1) {
  background: #fcb95a;
  color: #fff;
}

.t_btn button:nth-of-type(2) {
  background: #4c6fef;
  color: #fcf9f9;
  margin-left: 10%;
  float: right;
}

/*选择按钮*/
input[type="text"] {
  -webkit-appearance: none;
  appearance: none;
}

/**
 * Checkbox Four
 */
.checkboxFour {
  width: 0px;
  height: 0px;
  background-color: #fff;
  border-radius: 100%;
  position: relative;
  top: 5px;
}

/**
 * Create the checkbox button
 */
.checkboxFour label {
  display: block;
  width: 80vw;
  height: 6vw;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
  cursor: pointer;
  top: -1vw;
  position: absolute;
  z-index: 1;
  background: url(../../assets/question/huise.png) no-repeat;
  background-size: 6vw auto;
}

/**
 * Create the checked state
 */
.checkboxFour input[type="checkbox"]:checked+label {
  background: url(../../assets/question/xz.png) no-repeat;
  background-size: 6vw auto;
}

/* RADIO */
.regular-radio {
  display: none;
}

.regular-radio+label {
  width: 20px;
  height: 20px;
  -webkit-appearance: none;
  appearance: none;
  /* background: url(img/icon2_w3c1.png) no-repeat; */
  background-size: 20px auto;
  transition: all 0.5s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
  border-radius: 50px;
  display: inline-block;
  position: relative;
  top: 6px;
}

.regular-radio:checked+label:after {
  content: " ";
  width: 10px;
  height: 10px;
  border-radius: 50px;
  position: absolute;
  top: 4px;
  left: 4px;
  font-size: 32px;
}

.regular-radio:checked+label {
  /* background: url(../../assets/question/icon2_w3c2.png) no-repeat; */
  background-size: 20px auto;
  color: #99a1a7;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05),
    inset 15px 10px -12px rgba(255, 255, 255, 0.1),
    inset 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.regular-radio+label:active,
.regular-radio:checked+label:active {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px 1px 3px rgba(0, 0, 0, 0.1);
}

.big-radio+label {
  padding: 16px;
}

.big-radio:checked+label:after {
  width: 20px;
  height: 20px;
  left: 4px;
  top: 4px;
}

/*遮罩层*/
.ts_mask {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  z-index: 99;
  top: 0vw;
  display: none;
}

.tishi {
  width: 70%;
  height: 50vw;
  background: url(../../assets/question/mxz_ts.png) center no-repeat;
  float: left;
  background-size: 100% auto;
  margin: 50vw 15%;
}

.qd {
  width: 50%;
  height: 10vw;
  margin: 30vw 25% 0 25%;
}

/*完成调研*/
.finish_ion {
  width: 100%;
  height: 50vw;
  margin: 0 auto;
}

.ion {
  width: 50%;
  height: 35vw;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  margin: 0 25%;
}

.ion img {
  width: 80%;
  height: auto;
  margin-top: 2.7vw;
  margin-left: 10%;
}

.wz {
  width: 100%;
  height: 15vw;
  font-size: 4vw;
  text-align: center;
  line-height: 15vw;
  letter-spacing: 0.3vw;
  font-family: "黑体";
}

.words {
  width: 100%;
  height: 40vw;
  margin: 0 auto;
  border-radius: 5px;
  background: #fff;
  position: relative;
}

.words textarea {
  width: 96%;
  height: 36vw;
  visibility: visible;
  background: #fff;
  margin-top: 2vw;
  color: #999;
  margin-left: 2%;
  overflow: hidden;
}

.words input {
  width: 26%;
  height: 8vw;
  background: #d8d8d8;
  position: absolute;
  z-index: 88;
  right: 3vw;
  top: 30vw;
  font-size: 4vw;
  line-height: 8vw;
  text-align: center;
  border-radius: 6px;
  visibility: visible;
}

.botton_t {
  width: 100%;
  height: 10vw;
  float: left;
  margin: 5vw auto;
}

.botton_t input {
  width: 100%;
  height: 14vw;
  background: #fcb95a;
  border-radius: 6px;
  text-align: center;
  line-height: 14vw;
  font-size: 5vw;
  color: #fff;
  visibility: visible;
}

.share {
  width: 90%;
  height: auto;
  margin: 0 5%;
}

.share img {
  width: 100%;
  height: auto;
}

.van-overlay {
  z-index: 999 !important;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.smokingYear {
  height: 40px;
  width: 100%;
  font-size: 18px;
  color: #4a4a4a;
  display: flex;
  align-items: flex-end;
  border-bottom: 1px solid #9c9c9c;
  margin-top: 20px;
}

.smokingYear>div {
  color: #4a4a4a;
  height: 30px;
  line-height: 30p;
  display: flex;
  align-items: flex-end;
}

.dsa {
  height: 30px;
  min-width: 40%;
  visibility: visible !important;
  width: 18px;
  background: white !important;
}

.divboom {
  width: 94%;
  margin-left: 3%;
  height: 24%;
  // background: firebrick;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.divboom>button {
  visibility: visible !important;
  width: 35%;
  height: 65%;
  color: white;
}

.van-cell__value {
  margin-top: 0.8rem;
}

.van-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}
</style>
<style lang="scss">
.con_con .van-field__label {
  width: 17% !important;
  font-size: 17px !important;
}

.con_con .van-cell__value {
  border-bottom: 1px solid;
}
</style>