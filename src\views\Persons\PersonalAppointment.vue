<template>
  <div class="personal-appointment">
    <div>
      <!-- <img
        class="appointment-img"
        src="../../assets/icon/personal-appointment.png"
        alt=""
      /> -->

      <div class="historyBtn">
        <div class="button_index">
          <van-button @click="fhindex" icon="wap-home" type="primary" size="small"
            color="linear-gradient(to right, rgb(90 164 241),  rgb(90 164 241))">
            首页
          </van-button>
        </div>
      </div>
      <div style="margin-top: 7.5rem;"></div>

      <div class="appointment-btn" @click="toAddItemQ('question')">
        问卷定制个性化体检
      </div>
      <div class="appointment-title">其他体检方式</div>
      <div class="appointment-packages">
        <div class="packages-item" @click="onRoutine">
          <img
            class="packages-routine"
            src="../../assets/icon/routine.png"
            alt=""
          />
          <div class="packages-text">
            <div class="packages-title">常规体检套餐</div>
            <div class="packages-desc">
              常按性别、年龄等基本信息快速查找常规体检套餐。
            </div>
          </div>
          <img
            class="packages-enter"
            src="../../assets/icon/enter.png"
            alt=""
          />
        </div>
        <div class="packages-item" @click="toAddItem('person')">
          <img
            class="packages-routine"
            src="../../assets/icon/optional.png"
            alt=""
          />
          <div class="packages-text">
            <div class="packages-title">自选体检项目</div>
            <div class="packages-desc">
              了解自身体检需求，能自主选择体检项目。
            </div>
          </div>
          <img
            class="packages-enter"
            src="../../assets/icon/enter.png"
            alt=""
          />
        </div>

        <!-- <div class="packages-item" @click="toAddItemQ('question')">
          <img class="packages-routine" src="../../assets/icon/optional.png" alt="" />
          <div class="packages-text">
            <div class="packages-title">问卷推荐</div>
            <div class="packages-desc">
              问卷推荐体检项目。
            </div>
          </div>
          <img class="packages-enter" src="../../assets/icon/enter.png" alt="" />
        </div> -->
      </div>
    </div>
    <van-overlay :show="show">
      <div class="wrapper" @click.stop>
        <div class="wrapper-content">
          <div class="content-title">根据需求，请选择：</div>
          <div class="content-list">
            <div class="list-item" @click="PersonCommon('person')">
              <img src="../../assets/icon/personage.png" alt="" />
              <span>个人体检</span>
              <img src="../../assets/icon/enter.png" alt="" />
            </div>
            <div class="list-item" @click="PersonCommon('staff')">
              <img src="../../assets/icon/specialized.png" alt="" />
              <span>专科体检</span>
              <img src="../../assets/icon/enter.png" alt="" />
            </div>
            <div class="list-item" @click="PersonCommon('vehicle')">
              <img src="../../assets/icon/dignitary.png" alt="" />
              <span>贵宾体检</span>
              <img src="../../assets/icon/enter.png" alt="" />
            </div>
          </div>
        </div>
        <van-icon
          name="close"
          color="#fff"
          size="0.48rem"
          @click="show = false"
        />
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { ajax, storage } from "../../common";
export default {
  data() {
    return {
      show: false,
    };
  },
  created() {
    storage.session.delete("spTypeQ");
    storage.session.delete("onlyClus");
  },
  methods: {
    onRoutine() {
      this.show = true;
    },
    //个人体检选择
    PersonCommon(type) {
      storage.session.set("type", type);
      this.isCancleTip();
    },
    isCancleTip() {
      this.showTip = false;
      let type = storage.session.get("type");
      storage.session.set("spTypeQ","Question")
      switch (type) {
        case "person":
          this.$router.push({
            path: "/PersonIndex",
          });
          break;
        case "vehicle":
          this.$router.push({
            path: "/VehicleIndexs",
          });
          break;
        case "staff":
          this.$router.push({
            path: "/StaffIndex",
          });
          break;
      }
    },
    toAddItem(type) {
      // storage.session.delete("dataList")
      storage.session.set("type", type);
      this.$router.push({
        path: "/addClusItem",
      });
    },
    toAddItemQ(type) {
      // storage.session.delete("dataList")
      // storage.session.set("type", type);
      storage.session.set("type", "person");
      this.$router.push({
        path: "/questionIndex",
        query: {
          pageType: "question",
        },
      });
    },
    fhindex() {
      this.$router.push({
        path: "/",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.personal-appointment {
  height: 100%;
  .historyBtn {
    background-image: url("../../assets/icon/personal-appointment.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    height: 7rem;
  }

  .button_index {
    // width: 100%;
    font-size: 0.36rem;
    position: fixed;
    top: 0.2rem;
    left: 0;
    display: flex;
    align-items: center;
    // background: #fdfdfd;
    // box-shadow: 1px 1px 2px #fdfdfd;
  }
  .appointment-img {
    width: 100%;
    height: 6rem;
    margin-bottom: 0.8rem;
    display: block;
  }

  .appointment-btn {
    background: #1678ff;
    border-radius: 0.16rem;
    color: #fff;
    font-size: 0.36rem;
    text-align: center;
    padding: 0.2rem 0;
    margin: 0 0.76rem;
  }

  .appointment-title {
    font-size: 0.28rem;
    color: #4a4a4a;
    margin: 0.2rem 0 0.2rem 0.32rem;
  }

  .appointment-packages {
    .packages-item {
      background: #fff;
      display: grid;
      grid-template-columns: 1fr 4.7rem 1fr;
      grid-template-rows: 1fr;
      align-items: center;
      padding: 0.44rem 0.4rem 0.2rem 0.48rem;
      gap: 0.36rem;

      .packages-routine {
        width: 1.08rem;
      }

      .packages-text {
        display: grid;
        gap: 0.08rem;

        .packages-title {
          font-size: 0.28rem;
          color: #4a4a4a;
          font-weight: 500;
        }

        .packages-desc {
          font-size: 0.26rem;
          color: #666666;
        }
      }

      .packages-enter {
        width: 0.32rem;
      }
    }
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 0.2rem;

    .wrapper-content {
      width: 4.6rem;
      background: #f5f5f5;
      border-radius: 0.16rem;
      padding: 0.28rem;
      display: grid;
      grid-template-rows: 0.32rem 1fr;
      gap: 0.28rem;

      .content-title {
        font-size: 0.24rem;
        color: #4a4a4a;
      }

      .content-list {
        display: flex;
        flex-direction: column;
        gap: 0.24rem;

        .list-item {
          display: grid;
          grid-template-columns: 0.32rem 1fr 0.32rem;
          align-items: center;
          gap: 0.21rem;
          font-size: 0.28rem;
          color: #4a4a4a;
          background: #fff;
          box-shadow: 0px 2px 4px 0px rgba(153, 153, 153, 0.5);
          border-radius: 0.08rem;
          padding: 0.22rem;

          img {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
