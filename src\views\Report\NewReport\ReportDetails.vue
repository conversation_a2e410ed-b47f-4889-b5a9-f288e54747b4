<template>
  <div class="reportWrap">
    <div class="viewRepor">
      <section>
        <div class="box">
          <div class="healthCheckup" v-if="healthlist.length>0">
            <!-- 体格 -->
            <ul>
              <li v-for="(item,index) in healthlist" :key="index">
                <div class="infor1">
                  <div class="clearfix">
                    <p class="fl clearfix case">
                      <i class="fl">
                        <img src="../../../assets/report/checkicon2.png" alt />
                      </i>
                      <span class="fl">{{item.comb_name}}</span>
                    </p>
                    <p class="fr date">
                      <i>
                        <img src="../../../assets/report/calendar2.png" alt />
                      </i>
                      <span>{{report.reg_date}}</span>
                    </p>
                  </div>
                  <div class="clearfix">
                    <i class="fl">
                      <img src="../../../assets/report/checkicon6.png" alt />
                    </i>
                    <span class="fl">{{item.res_tag}}</span>
                  </div>
                  <div class="img">
                    <img src="../../../assets/report/doctor.png" alt />
                    <span>{{item.dept_doct}}</span>
                  </div>
                </div>
                <div class="infor2">
                  <span
                    v-for="(item2,index2) in item.detailList"
                    :key="index2"
                  >{{item2.item_name}}：{{item2.rec_result}}</span>
                </div>
              </li>
            </ul>
          </div>
          <!-- 检验 -->
          <div class="testItem" v-if="testlist.length>0">
            <ul>
              <li v-for="(item,index) in testlist" :key="index" class="a1">
                <div class="infor1">
                  <div class="clearfix">
                    <p class="fl clearfix case">
                      <i class="fl">
                        <img src="../../../assets/report/checkicon3.png" alt />
                      </i>
                      <span class="fl">{{item.comb_name}}</span>
                    </p>
                    <p class="fr date">
                      <i>
                        <img src="../../../assets/report/calendar2.png" alt />
                      </i>
                      <span>{{report.reg_date}}</span>
                    </p>
                  </div>
                  <div class="clearfix">
                    <i class="fl">
                      <img src="../../../assets/report/checkicon6.png" alt />
                    </i>
                    <span class="fl">{{item.res_tag}}</span>
                  </div>
                  <div class="img">
                    <img src="../../../assets/report/doctor.png" alt />
                    <span>{{item.dept_doct}}</span>
                  </div>
                </div>
                <div class="infor2">
                  <h5 @click="listItems($event)">
                    <span>项目结果明细</span>
                    <img src="../../../assets/report/down.png" alt class="down" />
                  </h5>
                  <ul class="table">
                    <li class="costTitle">
                      <ul>
                        <li class="projectName">项目名称</li>
                        <li class="checkResult">检查结果</li>
                        <li class="unit">单位</li>
                        <li class="refValue">参考值</li>
                      </ul>
                    </li>

                    <li
                      v-for="(item2,index2) in item.detailList"
                      :key="index2"
                      :class="item2.hint!=''?'outLimit':'' "
                    >
                      <ul>
                        <li class="projectName">{{item2.item_name}}</li>
                        <li class="checkResult">{{item2.rec_result}} {{item2.hint}}</li>
                        <li class="unit">{{item2.unit}}</li>
                        <li class="refValue">{{item2.ref_lower}}-{{item2.ref_upper}}</li>
                      </ul>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </div>

          <div class="checkItem" v-if="checklist.length>0">
            <!-- 检查 -->
            <ul style="margin-bottom: 1rem;">
              <li v-for="(item,index) in checklist" :key="index">
                <div class="infor1">
                  <div class="clearfix">
                    <p class="fl clearfix case">
                      <i class="fl">
                        <img src="../../../assets/report/checkicon7.png" alt />
                      </i>
                      <span class="fl">{{item.comb_name}}</span>
                    </p>
                    <p class="fr date">
                      <i>
                        <img src="../../../assets/report/calendar2.png" alt />
                      </i>
                      <span>{{report.reg_date}}</span>
                    </p>
                  </div>
                  <div class="clearfix">
                    <i class="fl">
                      <img src="../../../assets/report/checkicon6.png" alt />
                    </i>
                    <span class="fl">{{item.res_tag}}</span>
                  </div>
                  <div class="img">
                    <img src="../../../assets/report/doctor.png" alt />
                    <span>{{item.dept_doct}}</span>
                  </div>
                </div>
                <div class="infor2">
                  <span
                    v-for="(item2,index2) in item.detailList"
                    :key="index2"
                  >{{item2.item_name}}：{{item2.rec_result}}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </section>
    </div>

    <!--网页尾部-->
    <div class="footertab">
      <div class="footer_item" @click="gotourl('/ReportGuide')">
        <span>报告导读</span>
      </div>
      <div class="footer_item" @click="gotourl('/ReportFinal')">
        <span>总检报告</span>
      </div>
      <div class="footer_item" @click="gotourl('/ReportSuggest')">
        <span>医师建议</span>
      </div>
      <div class="footer_item" @click="gotourl('/ReportMian')">
        <span>报告首页</span>
      </div>
    </div>
  </div>
</template>

<script>
import { dataUtils, ajax, storage } from '@/common'
import { toolsUtils } from '@/common'
import apiUrls from '@/config/apiUrls';
import $ from 'jquery'
export default {
  components: {
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      healthlist: [],
      testlist: [],
      checklist: [],
      otherlist: [],
      report: {},

    };
  },
  created() {
    try {
      this.report = JSON.parse(storage.session.get('report'));
    } catch (error) {

    }
    this.disposeRes()
  },
  mounted() {
    //   $('h5').click();//展开检验报告详情
  },
  methods: {
    gotourl(url) {
      this.$router.push({ path: url });
    },
    disposeRes() {
      var res = this.report;
      this.healthlist = [];
      this.testlist = [];
      this.checklist = [];
      //根据check_cls分类
      for (var i = 0; i < res.itemlist.length; i++) {
        switch (res.itemlist[i].check_cls) {
          case "0": this.healthlist.push(res.itemlist[i]); break;
          case "1": this.checklist.push(res.itemlist[i]); break;
          case "2": this.testlist.push(res.itemlist[i]); break;
          default: this.otherlist.push(res.itemlist[i]);
        }
      }
      // console.log("healthlist",this.healthlist);
      // console.log("testlist",this.testlist);
      // console.log("checklist",this.checklist);

    },
    listItems(event) {
      //下拉
      // target
      var h5Click = $(event.currentTarget);
      var table = $(event.currentTarget).next();
      var img = $(event.currentTarget).find('img').first();

      table.slideUp(200);
      img.attr("src", require("@/assets/report/down.png"));

      var thisLi = h5Click.next();
      var thisImg = h5Click.find("img");
      if (thisLi.is(":hidden")) {
        thisLi.slideDown(200);
        thisImg.attr("src", require("@/assets/report/up.png"));
      } else {
        thisLi.slideUp(200);
        thisImg.attr("src", require("@/assets/report/down.png"));
      }
    }
  },
  computed: {
    newtitle: function () {
      return this.title;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.reportWrap {
  position: relative;
  width: 100%;
  min-height: 100%;
  height: 100% !important;
  height: 100%;
  background-color: #fff;
  min-width: 320px;
  max-width: 750px;
  margin: auto;
}
.viewRepor {
  width: 100%;
  height: 100%;
  position: relative;
}
.viewRepor header {
  width: 100%;
}
.viewRepor header .headInfor {
  padding: 0.2rem 0.78rem 0.12rem 0;
  margin-left: 0.86rem;
  position: relative;
  border-bottom: 0.02rem solid #e0e0e0;
  margin-bottom: 0.04rem;
}
.viewRepor header .headInfor .portrait {
  position: absolute;
  width: 0.96rem;
  height: 0.96rem;
  top: 0.16rem;
  left: -0.7rem;
  border-radius: 50%;
}
.viewRepor header .headInfor .fl p:nth-child(1),
.viewRepor header .headInfor .fr p:nth-child(1) {
  font-size: 0.28rem;
  color: #4a4a4a;
  line-height: 0.4rem;
}
.viewRepor header .headInfor .fl p:nth-child(2),
.viewRepor header .headInfor .fr p:nth-child(2) {
  font-size: 0.28rem;
  color: #9e9e9e;
  line-height: 0.4rem;
}
.viewRepor header .headInfor .fl {
  text-align: left;
  padding-left: 0.42rem;
}
.viewRepor header .headInfor .fr {
  text-align: right;
  padding-right: 0.24rem;
}
.viewRepor header .headInfor .rightIcon {
  position: absolute;
  width: 0.56rem;
  height: 0.64rem;
  top: 0.24rem;
  right: 0.22rem;
}
.viewRepor header .checkReview {
  padding: 0.2rem 0.22rem 0.12rem 0.04rem;
  overflow: hidden;
}
.viewRepor header .checkReview .left {
  width: 1.2rem;
  border-right: 0.02rem solid #e0e0e0;
  text-align: center;
  padding: 0.32rem 0;
  float: left;
  box-sizing: border-box;
  height: 3rem;
}
.viewRepor header .checkReview .left .leftIcon {
  width: 0.48rem;
  // display: inline;
}
.viewRepor header .checkReview .left span {
  display: block;
  line-height: 0.28rem;
  font-size: 0.24rem;
  margin-top: 0.08rem;
  color: #018bf0;
}
.viewRepor header .checkReview .right {
  width: 5.8rem;
  height: 3rem;
  font-size: 0.28rem;
  color: #646a6f;
  padding-left: 0.22rem;
  float: left;
  overflow-y: auto;
}
.viewRepor header .checkReview .right p {
  line-height: 0.4rem;
}

.viewRepor section {
  width: 100%;
  /* position: absolute; */
  top: 4.44rem;
  bottom: 0;
}

.viewRepor .box {
  width: 100%;
  /* position: absolute; */
  top: 0.2rem;
  bottom: 0;
  overflow-y: auto;
}
.viewRepor .box > div {
  width: 100%;
  height: 100%;
  /* position: absolute; */
  top: 0;
  left: 0;
  /* min-height: 100%; */
  height: auto !important;
  /* display: none; */
}
.viewRepor .box > div:nth-child(1) {
  display: block;
}
/*=====医生建议部分=====*/
.viewRepor .doctorAdvice ul {
  padding-bottom: 0.64rem;
  padding-left: 0.24rem;
  font-size: 0.28rem;
}
.viewRepor .doctorAdvice ul li:nth-child(1) {
  position: relative;
  border-bottom: 0.02rem solid #e0e0e0;
  padding-left: 0.98rem;
  padding-bottom: 0.24rem;
  /*height: 1.22rem;*/
  box-sizing: border-box;
}
.viewRepor .doctorAdvice ul li:nth-child(1) img {
  width: 0.92rem;
  height: 0.92rem;
  position: absolute;
  top: 0.23rem;
  left: -0.09rem;
  border-radius: 50%;
}
.viewRepor .doctorAdvice ul li:nth-child(1) .doc {
  margin-top: 0.34rem;
}
.viewRepor .doctorAdvice ul li:nth-child(1) .doc span {
  display: block;
  line-height: 0.4rem;
}
.viewRepor .doctorAdvice ul li:nth-child(1) .doc span:nth-child(1) {
  color: #9e9e9e;
}
.viewRepor .doctorAdvice ul li:nth-child(1) .doc span:nth-child(2) {
  color: #4a4a4a;
}
.viewRepor .doctorAdvice ul li:nth-child(1) .tip {
  width: 5rem;
  /*height: .86rem;*/
  color: #38abff;
  margin: 0.24rem 0 0 0.08rem;
  box-sizing: border-box;
  font-size: 0.24rem;
  line-height: 0.4rem;
  padding: 0.1rem 0.12rem 0.1rem 0.24rem;
  position: relative;
  background: url(../../../assets/report/viewReportIcon2.png) no-repeat;
  background-size: 100% 100%;
}
.viewRepor .doctorAdvice ul li:not(.head) {
  padding: 0.18rem 0.24rem 0 0;
  line-height: 0.4rem;
  font-size: 0.28rem;
  color: #646a6f;
}
/*=====体格检查部分 ,检验项目部分，检查项目部分,其他检查部分=====*/
.viewRepor .healthCheckup ul,
.viewRepor .checkItem ul,
.viewRepor .testItem > ul,
.viewRepor .otherItems ul {
  /* padding-bottom: .64rem; */
  padding-left: 0.24rem;
}
.viewRepor .healthCheckup ul li,
.viewRepor .checkItem ul li,
.viewRepor.testItem > ul > li,
.viewRepor .otherItems ul li {
  border-bottom: 0.02rem solid #e0e0e0;
  // padding-top: .2rem;
  padding-bottom: 0.22rem;
}
.viewRepor .healthCheckup ul li .img,
.viewRepor .checkItem ul li .img,
.viewRepor .testItem > ul > li .img,
.viewRepor .otherItems ul li .img {
  position: absolute;
  right: 0.24rem;
  top: 0;
  width: 0.88rem;
  text-align: center;
  margin-left: 0.24rem;
}
.viewRepor .healthCheckup ul li .img img,
.viewRepor .checkItem ul li .img img,
.viewRepor .testItem > ul > li .img img,
.viewRepor .otherItems ul .img img {
  width: 0.58rem;
  height: 0.58rem;
  // display: inline;
}
.viewRepor .healthCheckup ul li .img span,
.viewRepor .checkItem ul li .img span,
.viewRepor .testItem > ul > li .img span,
.viewRepor .otherItems ul li .img span {
  display: block;
  text-align: center;
  font-size: 0.24rem;
  color: #9e9e9e;
}
.viewRepor .healthCheckup li .infor1,
.viewRepor .checkItem ul li .infor1,
.viewRepor .testItem > ul > li .infor1,
.viewRepor .otherItems ul li .infor1 {
  position: relative;
}
.viewRepor .healthCheckup li .infor1 p,
.viewRepor .checkItem ul li .infor1 p,
.viewRepor .testItem > ul > li .infor1 p,
.viewRepor .otherItems ul li .infor1 p {
  display: inline-block;
}
.viewRepor .healthCheckup li .infor1 .case,
.viewRepor .checkItem ul li .infor1 .case,
.viewRepor .testItem > ul > li .infor1 .case,
.viewRepor .otherItems ul .infor1 .case {
  width: 3.7rem;
}
.viewRepor .healthCheckup li .infor1 .date,
.viewRepor .checkItem ul li .infor1 .date,
.viewRepor .testItem > ul > li .infor1 .date,
.viewRepor .otherItems ul li .infor1 .date {
  margin-right: 1.36rem;
}
.viewRepor .healthCheckup li .infor1 div:not(.img),
.viewRepor .checkItem ul li .infor1 div:not(.img),
.viewRepor .testItem > ul > li .infor1 div:not(.img),
.viewRepor .otherItems ul .infor1 div:not(.img) {
  font-size: 0.28rem;
  color: #373c40;
  line-height: 0.38rem;
  margin-bottom: 0.16rem;
}
.viewRepor .healthCheckup li .infor1 div:nth-child(2),
.viewRepor .checkItem ul li .infor1 div:nth-child(2),
.viewRepor .testItem > ul > li .infor1 div:nth-child(2),
.viewRepor .otherItems ul li .infor1 div:nth-child(2) {
  margin-right: 1.36rem;
}
.viewRepor .healthCheckup li .infor1 div:nth-child(2) i,
.viewRepor .healthCheckup li .infor1 .case i,
.viewRepor .checkItem ul li .infor1 div:nth-child(2) i,
.viewRepor .checkItem li .infor1 .case i,
.viewRepor .testItem > ul > li .infor1 div:nth-child(2) i,
.viewRepor .testItem > ul > li .infor1 .case i,
.viewRepor .otherItems ul li .infor1 div:nth-child(2) i,
.viewRepor .otherItems li .infor1 .case i {
  width: 0.38rem;
}
.viewRepor .healthCheckup li .infor1 .case span,
.viewRepor .checkItem li .infor1 .case span,
.viewRepor .testItem > ul > li .infor1 .case span,
.viewRepor .otherItems li .infor1 .case span {
  width: 3rem;
  margin-left: 0.15rem;
}
.viewRepor .healthCheckup li .infor1 div:nth-child(2) span,
.viewRepor .checkItem ul li .infor1 div:nth-child(2) span,
.viewRepor .testItem > ul > li .infor1 div:nth-child(2) span,
.viewRepor .otherItems ul li .infor1 div:nth-child(2) span {
  width: 5.35rem;
  margin-left: 0.15rem;
}
.viewRepor .healthCheckup li .infor1 div:not(.img) img,
.viewRepor .checkItem ul li .infor1 div:not(.img) img,
.viewRepor .testItem > ul > li .infor1 div:not(.img) img,
.viewRepor .otherItems ul li .infor1 div:not(.img) img {
  width: 0.38rem;
  height: 0.38rem;
  // display: inline;
  vertical-align: top;
}
.viewRepor .healthCheckup li .infor2,
.viewRepor .checkItem ul li .infor2,
.viewRepor .testItem > ul > li .infor2,
.viewRepor .otherItems ul li .infor2 {
  font-size: 0.28rem;
  line-height: 0.48rem;
  background-color: #f4f4f4;
  padding: 0.08rem 0.24rem 0.1rem 0.26rem;
  color: #646a6f;
  overflow: hidden;
}
.viewRepor .healthCheckup li .infor2 span,
.viewRepor .checkItem ul li .infor2 span,
.viewRepor .testItem > ul > li .infor2 span,
.viewRepor .otherItems ul li .infor2 span {
  min-width: 50%;
  max-width: 100%;
  height: auto;
  display: block;
  float: left;
}
/*=====检验项目部分=====*/
.viewRepor .testItem .infor2 {
  background-color: #f4f4f4;
}
.viewRepor .testItem .infor2 h5 {
  height: 0.7rem;
  line-height: 0.7rem;
  font-size: 0.28rem;
  border-bottom: 0.16rem solid #dfdfdf;
  position: relative;
  margin: 0;
}
.viewRepor .testItem .infor2 h5 img {
  width: 0.24rem;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -0.07rem;
}

.viewRepor .testItem .infor2 .table {
  overflow: hidden;
  padding-right: 0.03rem;
  font-size: 0.24rem;
  display: none;
}
.viewRepor .testItem .infor2 .table > li ul {
  overflow: hidden;
}
.viewRepor .testItem .infor2 .table > li li {
  float: left;
  min-height: 0.34rem;
}
.viewRepor .testItem .infor2 .table .costTitle {
  color: #000;
}
.viewRepor .testItem .infor2 .table .outLimit {
  color: red;
}
.viewRepor .testItem .infor2 .table > li li.projectName {
  width: 40.60475482912333%;
  text-align: left;
}
.viewRepor .testItem .infor2 .table > li li.arrows {
  width: 1%;
  text-align: center;
}
.viewRepor .testItem .infor2 .table > li li.checkResult,
.viewRepor .testItem .infor2 .table > li li.unit,
.viewRepor .testItem .infor2 .table > li li.refValue {
  width: 17.07317073170732%;
  margin-right: 2.67459138187221%;
  text-align: center;
}
.viewRepor .testItem .infor2 .table > li li.refValue {
  margin-right: 0;
}

/*=====检查项目部分=====*/
/*=====其他检查部分=====*/
/*=====底部版权======*/
.footer {
  height: 0.64rem;
  line-height: 0.64rem;
  clear: both;
  text-align: center;
  color: #9b9b9b;
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 0.28rem;
}

.nothing {
  width: 100%;
}
.nothing div {
  width: 60%;
  margin: 10% auto 0;
}
.nothing div span {
  width: 100%;
  display: block;
  text-align: center;
  font-size: 0.48rem;
  color: #018bf0;
}

.reportWrap {
  position: relative;
  width: 100%;
  min-height: 100%;
  height: 100% !important;
  height: 100%;
  background-color: #fff;
  min-width: 320px;
  max-width: 750px;
  margin: auto;
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.3);
}
.report_popup {
  position: fixed;
  width: 91.5%;
  height: 82.78%;
  top: 7.62%;
  left: 4.25%;
  z-index: 2;
  border-radius: 8px;
  background-color: #fff;
}

.report_popup .popup_head {
  background-color: #428adf;
  height: 0.92rem;
  line-height: 0.92rem;
  color: #fff;
  border-radius: 8px 8px 0 0;
  padding-left: 0.3rem;
}
.report_popup .popup_head .title {
  font-size: 0.28rem;
  float: left;
  width: 76.27365356622999%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.report_popup .popup_head .cancel {
  float: right;
  color: #428adf;
  background-color: #fff;
  border-radius: 50px;
  width: 0.96rem;
  height: 0.56rem;
  text-align: center;
  line-height: 0.56rem;
  margin-top: 0.18rem;
  font-size: 0.44rem;
}

.report_popup .popup_content {
  position: absolute;
  top: 0.92rem;
  bottom: 0;
  left: 0;
  right: 0;
}
.report_popup .popup_content .current_results {
  width: 100%;
  height: 44%;
  padding: 0 0.28rem;
  box-sizing: border-box;
  position: relative;
}
.report_popup .popup_content .current_results .current_title {
  height: 0.76rem;
  line-height: 0.76rem;
  font-size: 0.28rem;
  color: #4a4a4a;
}
.report_popup .popup_content .current_results .current_main {
  font-size: 0.28rem;
  color: #9b9b9b;
  line-height: 0.4rem;
  position: absolute;
  top: 0.76rem;
  right: 0.28rem;
  left: 0.28rem;
  bottom: 0.32rem;
  overflow-y: scroll;
}

.report_popup .popup_content .history_results {
  width: 100%;
  height: 56%;
  background: #f5f7fa;
  box-shadow: 0 -2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 0 0.28rem;
  box-sizing: border-box;
  position: relative;
}
.report_popup .popup_content .history_results .history_title {
  overflow: hidden;
  font-size: 0.28rem;
  height: 0.76rem;
  line-height: 0.76rem;
}
.report_popup .popup_content .history_results .history_title .title_left {
  color: #4a4a4a;
}
.report_popup .popup_content .history_results .history_title .title_right {
  color: #018bf0;
}
.report_popup .popup_content .history_results .history_main {
  position: absolute;
  top: 0.76rem;
  bottom: 0;
  left: 0.28rem;
  right: 0.28rem;
}
.report_popup .popup_content .history_results .history_main .history_page {
  font-size: 0.28rem;
  color: #9b9b9b;
  line-height: 0.4rem;
  height: 100%;
  overflow-y: scroll;
}

.report_popup .popup_content .history_results .history_main .swiper-container {
  height: 100%;
}
.report_popup .popup_content .history_results .history_main .swiper-pagination {
  position: absolute;
  height: 1.04rem;
  line-height: 1.04rem;
  bottom: 0rem;
}
.report_popup .popup_content .history_results .history_main .swiper-wrapper {
  height: 79%;
}

/*滚动条样式*/
.current_main::-webkit-scrollbar,
.history_page::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
.current_main::-webkit-scrollbar-thumb,
.history_page::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 2px;
  // -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.3);
  background: rgba(0, 0, 0, 0.3);
}
.current_main::-webkit-scrollbar-track,
.history_page::-webkit-scrollbar-track {
  border-radius: 2px;
  // -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0, 0, 0, 0.1);
}

.hisDiv1 {
  font-size: 0.28rem;
  line-height: 0.48rem;
  background-color: #f4f4f4;
  padding: 0.08rem 0.24rem 0.1rem 0.26rem;
  color: #646a6f;
  overflow: hidden;
}
.hisDiv1 span {
  min-width: 50%;
  max-width: 100%;
  height: auto;
  display: block;
  float: left;
}

.hisDiv2 {
  font-size: 0.28rem;
  line-height: 0.4rem;
  background-color: #f4f4f4;
  padding: 0.08rem 0.24rem 0.1rem 0.26rem;
  color: #646a6f;
  overflow: hidden;
}
.hisDiv2 h5 {
  height: 0.7rem;
  line-height: 0.7rem;
  font-size: 0.24rem;
  border-bottom: 0.2rem solid #dfdfdf;
  position: relative;
}

.hisDiv2 .table {
  overflow: hidden;
  padding-right: 0.03rem;
  font-size: 0.24rem;
}
.hisDiv2 .table > li ul {
  overflow: hidden;
}
.hisDiv2 .table > li li {
  float: left;
  min-height: 0.34rem;
}
.hisDiv2 .table .costTitle {
  color: #000;
}
.hisDiv2 .outLimit {
  color: red;
}
.hisDiv2 .table > li li.projectName {
  width: 40.60475482912333%;
  text-align: left;
}
.hisDiv2 .table > li li.arrows {
  width: 1%;
  text-align: center;
}
.hisDiv2 .table > li li.checkResult,
.hisDiv2 .table > li li.unit,
.hisDiv2 .table > li li.refValue {
  width: 17.07317073170732%;
  margin-right: 2.67459138187221%;
  text-align: center;
}
.hisDiv2 .table > li li.refValue {
  margin-right: 0;
}

.footertab {
  width: 7.5rem;
  height: 0.88rem;
  position: fixed;
  bottom: 0;
  left: 0;
}
.footertab .footer_item {
  width: calc(7.5rem / 4);
  height: 0.88rem;
  line-height: 0.88rem;
  float: left;
  // text-align: center;
  background-color: #489eea;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footertab .footer_item span {
  font-family: PingFangSC-Medium;
  display: inline-block;
  /* width: 1.28rem; */
  height: 0.44rem;
  line-height: 0.44rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.a1 {
  border-bottom: 0.02rem solid #e0e0e0;
  padding-bottom: 0.22rem;
}
</style>

