<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import { storage, ajax } from "./common";
import apiUrls from "./config/apiUrls";
import { Toast } from "vant";
import { authService } from "./service";
import qs from "qs";

export default {
  components: {
    // HeaderBar
  },
  data() {
    return {};
  },
  created() {},
  mounted() {
    var token = authService.getToken();
    if (token == null) {
      //去获取token
      this.apiGetToken();
    }
  },
  methods: {
    //微信授权登录
    apiGetToken() {
      var that = this;
      var data = {
        //认证类型
        grant_type: "password",
        client_id: "clientanduser",
        client_secret: "secret",
        name: "krinfo",
        code: "password",
        scope: "api1",
      };
      //注意这里需要使用fromdata的请求
      ajax
        .post(apiUrls.getToken, data)
        .then((r) => {
          r = r.data.returnData;
          // console.log(r);
          authService.setToken(r);
        })
        .catch((e) => {
          console.log(e);
        });
    },
  },
};
</script>

<style lang="scss">
body {
  background: #efefefe8;
}
#app {
  height: 100%;
  -webkit-overflow-scrolling: touch;
}
</style>
