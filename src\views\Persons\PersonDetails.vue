<template>
  <div>
    <div id="details">
      <!-- 入职套餐 -->
      <div class="detailsDiv">
        <div class="detailsTop">
          <div>
            <div class="TopImg" v-if="dataList.sex == '1'">
              <img src="../../assets/detailsMan.png" alt />
            </div>
            <div class="TopImg" v-else-if="dataList.sex == '2'">
              <img src="../../assets/detailsWoman.png" alt />
            </div>
            <div class="TopImg" v-else>
              <img src="../../assets/detailsSex01.png" alt />
            </div>
          </div>
          <!-- <div v-else>         
                    <div class="TopImg">
                        <img src="../../assets/detailsSex01.png" alt="">
                    </div>               
          </div>-->
          <div class="TopTitle">
            <span>{{ dataList.clus_Name }}</span>
          </div>
          <div class="TopBtn">
            <div>
              <span>基础套餐</span>
            </div>
          </div>
        </div>
      </div>

      <!-- tab -->
      <div class="detailsDiv TabDiv">
        <div class="detailsTab">
          <div class="TabBtn">
            <div
              :class="[BtnState ? 'BtnBlueL' : 'BtnWhiteL']"
              @click="btnIntroduction"
            >
              套餐简介
            </div>
            <!-- <div :class="[!BtnState?'BtnBlueR':'BtnWhiteR']" @click="btnKnow">体检须知</div> -->
          </div>
          <div class="TabText">
            <div v-if="TabText" class="TabSpan">
              <span>{{ PersonalSpan }}</span>
            </div>
            <div v-else>
              <div
                class="NoticeDiv"
                v-for="(item, index) in NoticeDiv"
                :key="index"
              >
                <div class="NoticeNumber">
                  <span>{{ index + 1 }}.</span>
                </div>
                <div class="NoticeText">
                  <span>{{ item.NoticeText }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="bottomDiv"></div>
        </div>
      </div>

      <!-- 套餐项目 -->
      <div class="SetMeal">
        <div class="detailsDiv SetMealBottom">
          <div class="SetMealA">
            <div class="SetMealImg">
              <img src="../../assets/SetMeal.png" alt="套餐项目" />
            </div>
            <span>套餐项目&nbsp;&nbsp;({{ detailed.length }}项)</span>
          </div>
        </div>
        <div class="detailsDiv">
          <div
            class="detailed"
            v-for="(items, indexS) in detailed"
            :key="indexS"
          >
            <div class="detailedTitle">
              <span>{{ items.comb_Name }}</span>
            </div>
            <div class="detailedText">
              <span>{{ items.note }}</span>
            </div>
            <div class="detailedBottom"></div>
          </div>

          <!-- <van-collapse v-model="activeNames">
            <van-collapse-item
              v-for="(items,indexS) in detailed"
              :key="indexS"
              :title="items.comb_Name"
              :name="items.comb_Code"
            >{{items.note}}</van-collapse-item>
          </van-collapse> -->
        </div>
      </div>

      <div class="footFixed">
        <div class="footLeft">
          <div class="LeftBtn">
            <span>￥</span>
            <span>{{ dataList.price }}</span>
          </div>
        </div>
        <div class="footMiddle">
          <span>您需自费的项目有{{ detailed.length }}个</span>
        </div>
        <div class="footRight" @click="isAddTips">
          <span>下一步</span>
        </div>
      </div>
      <div class="footDiv"></div>

      <van-overlay :show="show_material" v-show="show_material">
        <div style="display: flex; justify-content: center; padding-top: 90%">
          <van-loading type="spinner" color="#fdfdfd"
            >计算试管、材料费等...</van-loading
          >
        </div>
      </van-overlay>
    </div>
  </div>
</template>
<script>
import { storage, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      dataList: [],
      PersonalSpan: "",
      img: "",
      // Tab按钮样式
      BtnState: true,
      // Tab页文本显示
      TabText: true,
      // 体检须知文本
      NoticeDiv: [
        {
          NoticeText:
            "提前预约，为了成功提交订单，您最晚要在体检前1天（具体以网站公示的号源情况为准）预订，请尽早预订。",
        },
        {
          NoticeText:
            "如入职单位对体检项目有特殊要求，请于体检日在我中心前台进行具体咨询与调整。",
        },
        {
          NoticeText: "以上所有解释权归本健康体检中心所有！",
        },
      ],
      // 项目套餐
      detailed: [],
      // 套餐长度
      SetMealLength: 11,
      sex: "",
      type: "",
      //判断此版本是否需要加项功能
      IsAddClusItem: false,
      activeNames: [], //控制项目展开的面板列表
      add_comb_code: "", //仅有套餐时，用于同步的数据
      show_material: true,
    };
  },
  created() {
    this.dataList = JSON.parse(storage.session.get("dataList"));
    this.type = storage.session.get("type");
    // this.sex=this.dataList.sex;
    this.PersonalSpan = this.dataList.PersonalSpan;
    this.GetItemCombList(this.dataList.clus_Code);
    this.GetTotal(this.dataList.clus_Code);
    if (this.dataList.clus_Code == "108") {
      alert("驾驶员体检只能下午体检,请安排好体检时间");
    }
  },
  methods: {
    //获取套餐项目
    GetItemCombList(clus_Code) {
      var pData = {
        comb_code: clus_Code,
      };
      ajax
        .post(apiUrls.GetItemCombList, pData, { nocrypt: true })
        .then((r) => {
          var data = r.data.returnData;
          if (r.data.success) {
            this.detailed = data;
            // console.log("comb",data);
            let special = "";
            for (let i = 0; i < data.length; i++) {
              const elem = data[i];
              if (
                elem.comb_Code.trim() === "3034" ||
                elem.comb_Code.trim() === "3035" ||
                elem.comb_Code.trim() === "3003" ||
                elem.comb_Code.trim() === "3025"
              ) {
                special += elem.comb_Name;
              }
            }
            storage.session.set("clusIncludeItem", JSON.stringify(data));
            storage.session.set("clusIncludeSpecialItem", special);
          } else {
            alert("暂无项目数据");
          }
        })
        .catch((e) => {
          alert("系统繁忙！请稍后再试");
        });
    },
    // 套餐简介
    btnIntroduction() {
      this.BtnState = true;
      this.TabText = true;
    },
    // 体检须知
    btnKnow() {
      this.BtnState = false;
      this.TabText = false;
    },
    isAddTips() {
      // 替换原来的 confirm
      this.$dialog
        .confirm({
          title: "提示",
          message: "如有需求，可另行添加体检项目。是否添加体检项目？",
          confirmButtonText: "取消", // 这里将确认按钮文本改为"取消"
          cancelButtonText: "确定", // 这里将取消按钮文本改为"确定"
          confirmButtonOrder: "first", // 确认按钮放在前面
        })
        .then(() => {
          // 点击"取消"按钮时执行
          this.IsAddClusItem = false;
          this.ToCalendar();
        })
        .catch(() => {
          // 点击"确定"按钮时执行
          this.IsAddClusItem = true;
          this.ToCalendar();
        });
    },

    ToCalendar() {
      storage.session.set(
        "chooseItem",
        JSON.stringify({ totalPrice: this.dataList.price })
      );
      //加项
      if (this.IsAddClusItem) {
        this.$router.push({
          path: "/addClusItem",
          query: {
            isAdd: "T",
          },
        });
      } else {
        //仅套餐
        this.$router.push({
          path: "/PersonBookSum",
        });
      }
    },
    //计算总价
    GetTotal(clus_Code) {
      let pData = {
        clus_Code: clus_Code,
        // choose_comb_code: "",
        kw: "onlyClus",
      };
      ajax
        .post(apiUrls.CombsMaterial, pData, { nocrypt: true })
        .then((r) => {
          let data = JSON.parse(r.data.returnData)[0];
          // console.log("data", data);
          if (data.error == "OK") {
            this.add_comb_code = data.add_comb_code;
            this.dataList.price = data.total;
            storage.session.set("dataList", JSON.stringify(this.dataList));
            storage.session.set("onlyClus", this.add_comb_code);
            setTimeout(() => {
              this.show_material = false;
            }, 2000);
            // storage.session.set("onlyClus", data.add_comb_code);
            // this.$router.push({
            //   path: "/PersonBookSum"
            // });
          } else {
            Toast(data.error);
          }
        })
        .catch((e) => {
          alert("系统繁忙！请稍后再试");
        });
    },
  },
};
</script>
<style lang="scss">
.detailsDiv {
  width: 100%;
  background: white;
  box-sizing: border-box;
}

.detailsDiv .detailsTop {
  width: 92%;
  height: 1.24rem;
  margin: 0 auto;
  font-size: 0.28rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopImg {
  width: 1rem;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopTitle {
  width: calc(100% - 2.16rem);
  height: 100%;
  font-size: 0.36rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.TopTitle span {
  margin-left: 0.15rem;
}

.detailsTop .TopBtn {
  width: 1.4rem;
  height: 100%;
  color: #ffffff;
  letter-spacing: -0.04rem;
  display: flex;
  align-items: center;
}

.TopBtn div {
  width: 1.4rem;
  height: 0.48rem;
  background: #6a9be4;
  border-radius: 0.04rem;
  text-align: center;
  line-height: 0.48rem;
}

/* Tab */
.TabDiv {
  border-top: 0.04rem solid #dfe3e9;
}

.detailsDiv .detailsTab {
  width: 92%;
  margin: 0 auto;
}

.detailsTab .TabBtn {
  width: 100%;
  height: 0.64rem;
  font-size: 0.28rem;
  display: flex;
  margin-top: 0.24rem;
}

.TabBtn .BtnBlueL {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnWhiteL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnBlueR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 0 2px 2px 0;
}

.TabBtn .BtnWhiteR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 0 2px 2px 0;
}

.detailsTab .TabText {
  width: 100%;
  min-height: 1rem;
  font-size: 0.28rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin-top: 0.2rem;
}

.detailsTab .bottomDiv {
  width: 100%;
  height: 0.3rem;
}

.TabSpan {
  margin-top: 0.08rem;
}

.TabText .NoticeDiv {
  width: 100%;
  margin-top: 0.08rem;
  display: flex;
}

.NoticeDiv .NoticeNumber {
  width: 0.28rem;
}

.NoticeDiv .NoticeText {
  width: calc(100% - 0.28rem);
}

#details .SetMeal {
  width: 100%;
  font-size: 0.28rem;
  color: #4a4a4a;
  box-sizing: border-box;
  letter-spacing: -0.01px;
  margin-top: 0.16rem;
}

.SetMeal .SetMealBottom {
  border-bottom: 1px solid #dfe3e9;
}

.SetMeal .SetMealA {
  width: 92%;
  height: 0.84rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
}

.SetMealA .SetMealImg {
  width: 0.4rem;
  height: 0.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.SetMealImg img {
  margin-top: 3px;
}

.SetMealA span {
  margin-left: 0.1rem;
}

.SetMeal .detailed {
  width: 92%;
  margin: 0 auto;
  border-bottom: 1px solid #dfe3e9;
}

.detailed .detailedTitle {
  width: 100%;
  height: 0.8rem;
  display: flex;
  align-items: center;
}

.detailed .detailedText {
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
}

.detailed .detailedBottom {
  width: 100%;
  height: 0.24rem;
}

#details .footFixed {
  width: 100%;
  height: 1.16rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
}

.footFixed .footLeft {
  width: 2.2rem;
  height: 96%;
  margin-left: 4%;
  color: #d0021b;
  letter-spacing: -0.02px;
  display: flex;
  align-items: center;
}

.footLeft .LeftBtn {
  width: 100%;
  height: 0.56rem;
  border-right: 2px solid #9b9b9b;
}

.LeftBtn span:nth-child(2) {
  font-size: 0.4rem;
}

.footFixed .footMiddle {
  width: 3.08rem;
  height: 100%;
  color: #9b9b9b;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footFixed .footRight {
  width: 2.22rem;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
}

#details .footDiv {
  width: 100%;
  height: 1.16rem;
}
</style>
